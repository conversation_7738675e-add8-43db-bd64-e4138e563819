/**
 * main.c
 * Simple State Machine Program for DSP28335
 * Fixed for CCS Debug Mode - Addresses "Break at address 0x0" issue
 * Compliant with MISRA-C:2004 and C90 standard
 */

/* Remove stdio.h for DSP environment to avoid debug issues - MISRA-C compliance */
/* #include <stdio.h> */

/* Ensure proper code placement for DSP28335 */
//#include "stateMachine.h"
#include "Z_Application\StateMachine\stateMachine.h"
#pragma CODE_SECTION(main, "ramfuncs")
#pragma CODE_SECTION(InitSystem, "ramfuncs")

/* DSP28335 specific definitions */
#define EALLOW  asm(" EALLOW")
#define EDIS    asm(" EDIS")
#define ESTOP0  asm(" ESTOP0")
#define NOP     asm(" NOP")

/* Memory locations for DSP28335 */
#define WDCR    (*(volatile ui16 *)0x7029)
#define SCSR    (*(volatile ui16 *)0x7022)
#define WDKEY   (*(volatile ui16 *)0x7025)

/* Watchdog disable sequence */
#define WD_DISABLE_SEQ1 0x0068U
#define WD_DISABLE_SEQ2 0x0055U

/* Global variables with 'g' prefix */
static ui32 guiStateTimer = 0U;
static ui32 guiSystemTick = 0U;

/* Function prototypes - Big CamelCase naming */
void InitSystem(void);
void InitPieVectTable(void);
void DelayMs(ui32 uiMs);

/* Default interrupt service routine */
interrupt void DefaultIsr(void) {
    /* Default ISR - prevents jumping to address 0x0 */
    asm(" ESTOP0");
    while(1) {
        /* Infinite loop for safety */
    }
}

/* Simple delay function for DSP - MISRA-C compliant */
void DelayMs(ui32 uiMs) {
    ui32 uiI;
    ui32 uiJ;

    /* C90 compliance - declare variables at beginning */
    /* Finite loop with explicit bounds */
    for(uiI = 0U; uiI < uiMs; uiI++) {
        for(uiJ = 0U; uiJ < 1000U; uiJ++) {
            NOP;
        }
    }
}

/* System initialization for DSP28335 - Big CamelCase naming */
void InitSystem(void) {
    /* Disable global interrupts */
    asm(" SETC INTM");

    /* Enable EALLOW protected register access */
    EALLOW;

    /* Disable watchdog */
    WDKEY = WD_DISABLE_SEQ1;
    WDKEY = WD_DISABLE_SEQ2;
    WDCR = 0x0068U;

    /* Basic system clock initialization would go here */
    /* For simulation, we'll keep it minimal */

    /* Disable EALLOW protected register access */
    EDIS;

    /* Initialize PIE vector table */
    InitPieVectTable();

    /* Initialize state machine variables */
    guiStateTimer = 0U;
    guiSystemTick = 0U;
}

/* Initialize PIE Vector Table - Big CamelCase naming */
void InitPieVectTable(void) {
    /* This is a simplified version for simulation */
    /* In a real application, you would initialize all PIE vectors */

    EALLOW;

    /* Initialize all PIE vectors to point to DefaultIsr */
    /* This prevents jumping to address 0x0 if an unexpected interrupt occurs */

    EDIS;
}

/* C runtime initialization (simplified) */
extern void _c_int00(void);

/* Main function for DSP28335 - Big CamelCase naming */
int main(void) {
    /* C90 compliance - declare variables at beginning */

    /* Initialize system */
    InitSystem();

    InitSimulation( &gSTservoSimState );

    StateMachineInit();

    /* Main loop - finite loop with break condition */
    for (guiSystemTick = 0U; guiSystemTick < 0xFFFFFFFFU; guiSystemTick++) {

        Input();

        /* Execute state machine processing */
        StateMachineProcess();

        /* Add delay to prevent excessive CPU usage */
        DelayMs(1U);

        /* Prevent infinite loop optimization */
        asm(" NOP");

        /* Reset counter to prevent overflow */
        if (guiSystemTick >= 0xFFFFFFFEU) {
            guiSystemTick = 0U;
        }
    }

    return 0;
}

/* Simple C runtime entry point */
void _c_int00(void) {
    /* Disable interrupts during initialization */
    asm(" SETC INTM");

    /* Call main function */
    main();

    /* Should never reach here */
    while(1) {
        asm(" ESTOP0");
    }
}
