/**
 * @file externInputInterface.h
 * @brief External input interface for state machine transitions
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef EXTERN_INPUT_INTERFACE_H
#define EXTERN_INPUT_INTERFACE_H

#include "Lib\BackgroundInterface\BackgroundInterface.h"

/* External interface functions */
extern void Input(void);   /* input from outside */
extern void Output(void);   /* output to outside */

/* Motor control interface function - safe abstraction layer */
extern void MotorControlInterface_Run(ENUM_MOTION_CONTROL_MODE_SELECT Mode,
                              unsigned int CycleTime,
                              float TargetPosition,
                              float TargetSpeed,
                              float MaxSpeed,
                              float KP1,
                              float KP2);

#endif /* EXTERN_INPUT_INTERFACE_H */
