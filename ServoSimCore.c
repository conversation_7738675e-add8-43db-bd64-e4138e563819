/**
 * @file ServoSimCore.c
 * @brief Servo simulation core implementation
 * @note Compliant with MISRA-C:2004 and C90 standard
 */
#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "Z_Application\StateMachine\ServoSimCore.h"
//#include <math.h>
//#include <stdlib.h>

#define PI 3.14159265358979323846

/* Static function prototypes for internal use */
static void InitInputParameters(ServoSimState* pState);
static void InitOutputParameters(ServoSimState* pState);
static void InitControlParameters(ServoSimState* pState);
static void InitMotorParameters(ServoSimState* pState);
static void InitInternalStates(ServoSimState* pState);
static void InitSimulationControl(ServoSimState* pState);
static void ProcessPositionLoop(ServoSimState* pState, double* pdSpeedCommand);
static void ProcessSpeedLoop(ServoSimState* pState, double dSpeedCommand, double* pdCurrentCommandQ);
static void ProcessCurrentLoop(ServoSimState* pState, double dCurrentCommandD, double dCurrentCommandQ);
static void ProcessMotorModel(ServoSimState* pState);

/* Degree to radian conversion */
EXPORT double DegToRad(double dDeg) {
    return dDeg * PI / 180.0;
}

/* Radian to degree conversion */
EXPORT double RadToDeg(double dRad) {
    return dRad * 180.0 / PI;
}

/* Initialize input parameters */
static void InitInputParameters(ServoSimState* pState) {
    pState->dAcVoltage = 400.0;
    pState->dPositionCommand = 273000.0;
    pState->dSpeedLimit = 3000.0;
    pState->dAccelerationLimit = 10000.0;
    pState->dLoadTorque = 0.0;
    pState->dMotionSpeedCommand = 100.0;
}

/* Initialize output parameters */
static void InitOutputParameters(ServoSimState* pState) {
    pState->dMotorCurrentD = 0.0;
    pState->dMotorCurrentQ = 0.0;
    pState->dMotorVoltageD = 0.0;
    pState->dMotorVoltageQ = 0.0;
    pState->dMotorSpeed = 0.0;
    pState->dMotorPosition = 273000.0;
}

/* Initialize control parameters */
static void InitControlParameters(ServoSimState* pState) {
    pState->dPositionKp = 10.0;
    pState->dPositionKi = 0.1;
    pState->dSpeedKp = 5.0;
    pState->dSpeedKi = 1.0;
    pState->dCurrentKp = 20.0;
    pState->dCurrentKi = 10.0;
}

/* Initialize motor parameters */
static void InitMotorParameters(ServoSimState* pState) {
    pState->dMotorRs = 0.5;
    pState->dMotorLd = 0.001;
    pState->dMotorLq = 0.001;
    pState->dMotorInertia = 0.01;
    pState->dMotorDamping = 0.1;
    pState->dMotorPoles = 4.0;
    pState->dMotorFlux = 0.175;
}

/* Initialize internal state variables */
static void InitInternalStates(ServoSimState* pState) {
    pState->dPositionError = 0.0;
    pState->dSpeedError = 0.0;
    pState->dCurrentErrorD = 0.0;
    pState->dCurrentErrorQ = 0.0;
    pState->dPositionIntegral = 0.0;
    pState->dSpeedIntegral = 0.0;
    pState->dCurrentIntegralD = 0.0;
    pState->dCurrentIntegralQ = 0.0;
    pState->dElectricalAngle = 0.0;
}

/* Initialize simulation control */
static void InitSimulationControl(ServoSimState* pState) {
    pState->iRunning = 0;
    pState->dTimeStep = 0.001;  /* 1000μs */
    pState->dSimulationTime = 0.0;
    pState->iRealTimeUpdate = 0;
    pState->iModeControl = 0;/*positon control*/
}

/* Initialize simulation state - max 100 lines */
EXPORT void InitSimulation(ServoSimState* pState) {
    /* C90 compliance - declare variables at beginning */
    
    /* Initialize all subsystems */
    InitInputParameters(pState);
    InitOutputParameters(pState);
    InitControlParameters(pState);
    InitMotorParameters(pState);
    InitInternalStates(pState);
    InitSimulationControl(pState);
}

/* Process position loop - max 100 lines */
static void ProcessPositionLoop(ServoSimState* pState, double* pdSpeedCommand) {
    /* C90 compliance - declare variables at beginning */
    
    /* Position loop (deg) */
    pState->dPositionError = pState->dPositionCommand - pState->dMotorPosition;
    pState->dPositionIntegral += pState->dPositionError * pState->dTimeStep;

    /* Position integrator limiting */
    if (pState->dPositionIntegral > 100.0) {
        pState->dPositionIntegral = 100.0;
    }
    if (pState->dPositionIntegral < -100.0) {
        pState->dPositionIntegral = -100.0;
    }

    *pdSpeedCommand = pState->dPositionKp * pState->dPositionError +
                      pState->dPositionKi * pState->dPositionIntegral;
    
    /* Speed limiting (deg/s) */
    if (*pdSpeedCommand > pState->dSpeedLimit) {
        *pdSpeedCommand = pState->dSpeedLimit;
    } else if (*pdSpeedCommand < -pState->dSpeedLimit) {
        *pdSpeedCommand = -pState->dSpeedLimit;
    } else {
        /* No change needed */
    }
}

/* Process speed loop - max 100 lines */
static void ProcessSpeedLoop(ServoSimState* pState, double dSpeedCommand, double* pdCurrentCommandQ) {
    double dCurrentLimit;
    
    /* C90 compliance - declare variables at beginning */
    dCurrentLimit = 200.0; /* Maximum current limit */
    
    /* Speed loop (deg/s) */
    pState->dSpeedError = dSpeedCommand - pState->dMotorSpeed;
    pState->dSpeedIntegral += pState->dSpeedError * pState->dTimeStep;

    /* Speed integrator limiting */
    if (pState->dSpeedIntegral > 50.0) {
        pState->dSpeedIntegral = 50.0;
    }
    if (pState->dSpeedIntegral < -50.0) {
        pState->dSpeedIntegral = -50.0;
    }

    *pdCurrentCommandQ = pState->dSpeedKp * pState->dSpeedError +
                         pState->dSpeedKi * pState->dSpeedIntegral;
    
    /* Current command limiting */
    if (*pdCurrentCommandQ > dCurrentLimit) {
        *pdCurrentCommandQ = dCurrentLimit;
    } else if (*pdCurrentCommandQ < -dCurrentLimit) {
        *pdCurrentCommandQ = -dCurrentLimit;
    } else {
        /* No change needed */
    }
}

/* Process current loop - max 100 lines */
static void ProcessCurrentLoop(ServoSimState* pState, double dCurrentCommandD, double dCurrentCommandQ) {
    double dVoltageCommandD;
    double dVoltageCommandQ;
    double dDcVoltage;
    double dMaxVoltage;
    double dVoltageNorm;
    double dScale;
    
    /* C90 compliance - declare variables at beginning */
    
    /* Current loop - d axis */
    pState->dCurrentErrorD = dCurrentCommandD - pState->dMotorCurrentD;
    pState->dCurrentIntegralD += pState->dCurrentErrorD * pState->dTimeStep;
    
    /* Current integrator limiting */
    if (pState->dCurrentIntegralD > 2.0) {
        pState->dCurrentIntegralD = 2.0;
    }
    if (pState->dCurrentIntegralD < -2.0) {
        pState->dCurrentIntegralD = -2.0;
    }
    
    /* Current loop - q axis */
    pState->dCurrentErrorQ = dCurrentCommandQ - pState->dMotorCurrentQ;
    pState->dCurrentIntegralQ += pState->dCurrentErrorQ * pState->dTimeStep;
    
    /* Current integrator limiting */
    if (pState->dCurrentIntegralQ > 2.0) {
        pState->dCurrentIntegralQ = 2.0;
    }
    if (pState->dCurrentIntegralQ < -2.0) {
        pState->dCurrentIntegralQ = -2.0;
    }
    
    /* Voltage command calculation */
    dVoltageCommandD = pState->dCurrentKp * pState->dCurrentErrorD +
                       pState->dCurrentKi * pState->dCurrentIntegralD;
    dVoltageCommandQ = pState->dCurrentKp * pState->dCurrentErrorQ +
                       pState->dCurrentKi * pState->dCurrentIntegralQ;
    
    /* Rectifier and inverter (simplified model) */
    dDcVoltage = pState->dAcVoltage * 1.35; /* DC voltage after rectification */
    
    /* Voltage limiting (considering inverter voltage limit) */
    dMaxVoltage = dDcVoltage / 1.732; /* Maximum phase voltage */
    dVoltageNorm = sqrt(dVoltageCommandD * dVoltageCommandD + dVoltageCommandQ * dVoltageCommandQ);
    
    if (dVoltageNorm > dMaxVoltage) {
        dScale = dMaxVoltage / dVoltageNorm;
        dVoltageCommandD *= dScale;
        dVoltageCommandQ *= dScale;
    }
    
    pState->dMotorVoltageD = dVoltageCommandD;
    pState->dMotorVoltageQ = dVoltageCommandQ;
}

/* Process motor model - max 100 lines */
static void ProcessMotorModel(ServoSimState* pState) {
    double dElectricalAngleRaw;
    double dElectricalAngleRad;
    double dSpeedRad;
    double dED;
    double dEQ;
    double dDIdDt;
    double dDIqDt;
    double dTorque;
    double dNetTorque;
    double dAcceleration;
    double dAccelerationDeg;
    
    /* C90 compliance - declare variables at beginning */
    
    /* Calculate electrical angle (deg) - keep normalized for electrical calculations */
    dElectricalAngleRaw = pState->dMotorPosition * pState->dMotorPoles;
    pState->dElectricalAngle = fmod(dElectricalAngleRaw, 360.0);
    if (pState->dElectricalAngle < 0.0) {
        pState->dElectricalAngle += 360.0;
    }
    
    /* Electrical angle (rad) */
    dElectricalAngleRad = DegToRad(pState->dElectricalAngle);
    
    /* Motor model - permanent magnet synchronous motor */
    /* Speed (rad/s) */
    dSpeedRad = DegToRad(pState->dMotorSpeed);
    
    /* Back EMF */
    dED = -dSpeedRad * pState->dMotorPoles * pState->dMotorLq * pState->dMotorCurrentQ;
    dEQ = dSpeedRad * pState->dMotorPoles * (pState->dMotorFlux + pState->dMotorLd * pState->dMotorCurrentD);
    
    /* Current derivatives */
    dDIdDt = (pState->dMotorVoltageD - pState->dMotorRs * pState->dMotorCurrentD - dED) / pState->dMotorLd;
    dDIqDt = (pState->dMotorVoltageQ - pState->dMotorRs * pState->dMotorCurrentQ - dEQ) / pState->dMotorLq;
    
    /* Update currents */
    pState->dMotorCurrentD += dDIdDt * pState->dTimeStep;
    pState->dMotorCurrentQ += dDIqDt * pState->dTimeStep;
    
    /* Electromagnetic torque */
    dTorque = 1.5 * pState->dMotorPoles * (
        pState->dMotorFlux * pState->dMotorCurrentQ + 
        (pState->dMotorLd - pState->dMotorLq) * pState->dMotorCurrentD * pState->dMotorCurrentQ
    );
    
    /* Mechanical equation (including load torque) */
    dNetTorque = dTorque - pState->dLoadTorque - pState->dMotorDamping * dSpeedRad;
    dAcceleration = dNetTorque / pState->dMotorInertia;
    
    /* Convert to angular acceleration (deg/s^2) */
    dAccelerationDeg = RadToDeg(dAcceleration);
    
    /* Acceleration limiting */
    if (dAccelerationDeg > pState->dAccelerationLimit) {
        dAccelerationDeg = pState->dAccelerationLimit;
    } else if (dAccelerationDeg < -pState->dAccelerationLimit) {
        dAccelerationDeg = -pState->dAccelerationLimit;
    } else {
        /* No change needed */
    }
    
    /* Update speed and position */
    pState->dMotorSpeed += dAccelerationDeg * pState->dTimeStep;
    pState->dMotorPosition += pState->dMotorSpeed * pState->dTimeStep;

    /* Note: Remove position normalization to allow multi-turn control */
    /* This allows positions beyond 360 degrees like 720, 1080 degrees etc. */
    
    /* Update simulation time */
    pState->dSimulationTime += pState->dTimeStep;
}

/* Execute one simulation step - max 100 lines */
EXPORT void SimulationStep(ServoSimState* pState) {
    double dSpeedCommand;
    double dCurrentCommandQ;
    double dCurrentCommandD;

    /* C90 compliance - declare variables at beginning */
    dCurrentCommandD = 0.0; /* d-axis current command (field weakening control, simplified to 0) */

    if (pState->iRunning == 0) {
        return;
    }

    /* Handle real-time parameter updates */
    if (pState->iRealTimeUpdate != 0) {
        /* Real-time parameter update flag has been set, parameters have been updated through structure */
        /* Parameter validation or limiting can be added here */

        /* Reset real-time update flag */
        pState->iRealTimeUpdate = 0;
    }

    if (pState->iModeControl  == 0 ){
        /* Process control loops */
        ProcessPositionLoop(pState, &dSpeedCommand);
    }
    else if  (pState->iModeControl  == 1 ) {
        /* Speed mode */
        dSpeedCommand = pState->dMotionSpeedCommand;
        /* Apply speed limit */
        if (dSpeedCommand > pState->dSpeedLimit) {
            dSpeedCommand = pState->dSpeedLimit;
        } else if (dSpeedCommand < -pState->dSpeedLimit) {
            dSpeedCommand = -pState->dSpeedLimit;
        }
    }
    else{
        /* no action*/
    }
    ProcessSpeedLoop(pState, dSpeedCommand, &dCurrentCommandQ);
    ProcessCurrentLoop(pState, dCurrentCommandD, dCurrentCommandQ);
    ProcessMotorModel(pState);
}

/* Reset simulation - max 100 lines */
EXPORT void ResetSimulation(ServoSimState* pState) {
    /* C90 compliance - declare variables at beginning */

    /* Reset output parameters */
    pState->dMotorCurrentD = 0.0;
    pState->dMotorCurrentQ = 0.0;
    pState->dMotorVoltageD = 0.0;
    pState->dMotorVoltageQ = 0.0;
    pState->dMotorSpeed = 0.0;
    pState->dMotorPosition = 0.0;

    /* Reset internal state variables */
    pState->dPositionError = 0.0;
    pState->dSpeedError = 0.0;
    pState->dCurrentErrorD = 0.0;
    pState->dCurrentErrorQ = 0.0;
    pState->dPositionIntegral = 0.0;
    pState->dSpeedIntegral = 0.0;
    pState->dCurrentIntegralD = 0.0;
    pState->dCurrentIntegralQ = 0.0;
    pState->dElectricalAngle = 0.0;

    /* Reset simulation time */
    pState->dSimulationTime = 0.0;
}
