******************************************************************************
             TMS320C2000 Linker PC v18.1.4                     
******************************************************************************
>> Linked Sun Aug  3 15:27:32 2025

OUTPUT FILE NAME:   <testStateMachineAugment_Interface.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 000059a2


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  ZONE0                 00004000   00001000  000000a1  00000f5f  RWIX
  ZONE1                 00005000   00001000  00000adf  00000521  RWIX
  ZONE2                 00006000   00001000  00000000  00001000  RWIX
  ZONE3                 00007000   00001000  00000000  00001000  RWIX
  ZONE4                 00008000   00001000  00000000  00001000  RWIX
  ZONE5                 00009000   00001000  00000000  00001000  RWIX
  ZONE6                 0000a000   00001000  00000000  00001000  RWIX
  ZONE7                 0000b000   00001000  00000000  00001000  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000000  00000009  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX
  VECTORS               003fffc2   0000003e  00000000  0000003e  RWIX

PAGE 1:
  M0SARAM               00000000   00000400  00000300  00000100  RWIX
  M1SARAM               00000400   00000400  00000000  00000400  RWIX
  PIEDATA               00000ce0   00000020  00000000  00000020  RWIX
  L0SARAM               00008000   00001000  000000a1  00000f5f  RWIX
  L1SARAM               00009000   00001000  00000000  00001000  RWIX
  L2SARAM               0000a000   00001000  00000000  00001000  RWIX
  L3SARAM               0000b000   00001000  00000000  00001000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.cinit     0    00004000    00000060     
                  00004000    00000038     stateMachine.obj (.cinit)
                  00004038    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  00004046    0000000a     main.obj (.cinit)
                  00004050    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  00004055    00000005                       : _lock.c.obj (.cinit:__unlock)
                  0000405a    00000004                       : errno.c.obj (.cinit)
                  0000405e    00000002     --HOLE-- [fill = 0]

.pinit     0    00004000    00000000     UNINITIALIZED

ramfuncs   0    00004060    0000003f     
                  00004060    0000003f     main.obj (ramfuncs)

codestart 
*          0    0000409f    00000002     
                  0000409f    00000002     CodeStartBranch.obj (codestart)

.text      0    00005000    00000adf     
                  00005000    0000058a     ServoSimCore.obj (.text)
                  0000558a    00000229     stateMachine.obj (.text)
                  000057b3    000000ce     rts2800_fpu32.lib : e_fmodf.c.obj (.text)
                  00005881    00000099     externInputInterface.obj (.text)
                  0000591a    00000088     rts2800_fpu32.lib : fs_div28.asm.obj (.text)
                  000059a2    00000056                       : boot28.asm.obj (.text)
                  000059f8    00000029                       : exit.c.obj (.text)
                  00005a21    00000028     main.obj (.text)
                  00005a49    00000001     rts2800_fpu32.lib : startup.c.obj (.text)
                  00005a4a    00000026                       : e_sqrtf.c.obj (.text)
                  00005a70    00000024                       : cpy_tbl.c.obj (.text)
                  00005a94    0000001d                       : memcpy.c.obj (.text)
                  00005ab1    00000019                       : args_main.c.obj (.text)
                  00005aca    0000000a     main.obj (.text:retain)
                  00005ad4    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  00005add    00000002                       : pre_init.c.obj (.text)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot28.asm.obj (.reset)

vectors    0    003fffc2    00000000     DSECT

.stack     1    00000000    00000300     UNINITIALIZED
                  00000000    00000300     --HOLE--

.ebss      1    00008000    0000009b     UNINITIALIZED
                  00008000    0000008c     stateMachine.obj (.ebss)
                  0000808c    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  00008092    00000004     main.obj (.ebss)
                  00008096    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  00008098    00000002                       : _lock.c.obj (.ebss:__unlock)
                  0000809a    00000001                       : errno.c.obj (.ebss)

.econst    1    0000809c    00000006     
                  0000809c    00000004     rts2800_fpu32.lib : e_fmodf.c.obj (.econst:_Zero)
                  000080a0    00000002                       : e_fmodf.c.obj (.econst)

MODULE SUMMARY

       Module                     code   initialized data   uninitialized data
       ------                     ----   ----------------   ------------------
    .\
       ServoSimCore.obj           1418   0                  0                 
       stateMachine.obj           553    56                 140               
       externInputInterface.obj   153    0                  0                 
       main.obj                   113    10                 4                 
       CodeStartBranch.obj        2      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     2239   66                 144               
                                                                              
    D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\rts2800_fpu32.lib
       e_fmodf.c.obj              206    6                  0                 
       fs_div28.asm.obj           136    0                  0                 
       boot28.asm.obj             86     0                  0                 
       exit.c.obj                 41     14                 6                 
       e_sqrtf.c.obj              38     0                  0                 
       cpy_tbl.c.obj              36     0                  0                 
       memcpy.c.obj               29     0                  0                 
       args_main.c.obj            25     0                  0                 
       _lock.c.obj                9      10                 4                 
       errno.c.obj                0      4                  1                 
       pre_init.c.obj             2      0                  0                 
       startup.c.obj              1      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     609    34                 11                
                                                                              
       Stack:                     0      0                  768               
    +--+--------------------------+------+------------------+--------------------+
       Grand Total:               2848   100                923               


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000000       0 (00000000)     __stack

00008000     200 (00008000)     _guiTf65Condition
00008001     200 (00008000)     _guiTf910Condition
00008002     200 (00008000)     _guiTf64Condition
00008003     200 (00008000)     _guiTf13Condition
00008004     200 (00008000)     _guiTf36Condition
00008005     200 (00008000)     _guiTf53Condition
00008006     200 (00008000)     _guiTf83Condition
00008007     200 (00008000)     _gCurrentState
00008008     200 (00008000)     _guiTf95Condition
00008009     200 (00008000)     _guiTf105Condition
0000800a     200 (00008000)     _guiTf18Condition
0000800b     200 (00008000)     _guiTf57Condition
0000800c     200 (00008000)     _guiTf45Condition
0000800d     200 (00008000)     _guiTf73Condition
0000800e     200 (00008000)     _StatemachineRunToEmergencyFlag
0000800f     200 (00008000)     _stcStateMachine
00008013     200 (00008000)     _gstSEC

00008040     201 (00008040)     _gSTservoSimState

0000808c     202 (00008080)     ___TI_enable_exit_profile_output
0000808e     202 (00008080)     ___TI_cleanup_ptr
00008090     202 (00008080)     ___TI_dtors_ptr
00008096     202 (00008080)     __lock
00008098     202 (00008080)     __unlock
0000809a     202 (00008080)     _errno


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                            
----  -------   ----                            
0     00005000  .text                           
0     000059f8  C$$EXIT                         
0     0000591a  FS$$DIV                         
0     00005599  _ClrBit                         
0     00005aca  _DefaultIsr                     
0     00005000  _DegToRad                       
0     00005a21  _DelayMs                        
0     00005a3f  _InitPieVectTable               
0     0000512f  _InitSimulation                 
0     00004060  _InitSystem                     
0     00005881  _Input                          
0     00005889  _MotorControlInterface_Run      
0     000055a9  _MotorRun                       
0     00005888  _Output                         
0     00005011  _RadToDeg                       
0     00005535  _ResetSimulation                
0     0000558a  _SetBit                         
0     000054dd  _SimulationStep                 
0     000055c4  _StateMachineInit               
0     0000566b  _StateMachineProcess            
1     0000800e  _StatemachineRunToEmergencyFlag 
1     00000300  __STACK_END                     
abs   00000300  __STACK_SIZE                    
1     0000808e  ___TI_cleanup_ptr               
1     00008090  ___TI_dtors_ptr                 
1     0000808c  ___TI_enable_exit_profile_output
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
0     00004000  ___cinit__                      
0     00005adf  ___etext__                      
abs   ffffffff  ___pinit__                      
0     00005000  ___text__                       
0     00005ab1  __args_main                     
0     00005a43  __c_int00                       
1     00008096  __lock                          
0     00005adc  __nop                           
0     00005ad8  __register_lock                 
0     00005ad4  __register_unlock               
1     00000000  __stack                         
0     00005a49  __system_post_cinit             
0     00005add  __system_pre_init               
1     00008098  __unlock                        
0     000059f8  _abort                          
0     000059a2  _c_int00                        
0     00005a70  _copy_in                        
1     0000809a  _errno                          
0     000059fa  _exit                           
0     000057b3  _fmod                           
0     000057b3  _fmodf                          
1     00008007  _gCurrentState                  
1     00008040  _gSTservoSimState               
1     00008013  _gstSEC                         
1     00008009  _guiTf105Condition              
1     00008003  _guiTf13Condition               
1     0000800a  _guiTf18Condition               
1     00008004  _guiTf36Condition               
1     0000800c  _guiTf45Condition               
1     00008005  _guiTf53Condition               
1     0000800b  _guiTf57Condition               
1     00008002  _guiTf64Condition               
1     00008000  _guiTf65Condition               
1     0000800d  _guiTf73Condition               
1     00008006  _guiTf83Condition               
1     00008001  _guiTf910Condition              
1     00008008  _guiTf95Condition               
0     00004075  _main                           
0     00005a94  _memcpy                         
0     00005a4a  _sqrt                           
0     00005a4a  _sqrtf                          
1     0000800f  _stcStateMachine                
abs   ffffffff  binit                           
0     00004000  cinit                           
0     0000409f  code_start                      
0     00005adf  etext                           
abs   ffffffff  pinit                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                            
----  -------   ----                            
0     00004000  ___cinit__                      
0     00004000  cinit                           
0     00004060  _InitSystem                     
0     00004075  _main                           
0     0000409f  code_start                      
0     00005000  .text                           
0     00005000  _DegToRad                       
0     00005000  ___text__                       
0     00005011  _RadToDeg                       
0     0000512f  _InitSimulation                 
0     000054dd  _SimulationStep                 
0     00005535  _ResetSimulation                
0     0000558a  _SetBit                         
0     00005599  _ClrBit                         
0     000055a9  _MotorRun                       
0     000055c4  _StateMachineInit               
0     0000566b  _StateMachineProcess            
0     000057b3  _fmod                           
0     000057b3  _fmodf                          
0     00005881  _Input                          
0     00005888  _Output                         
0     00005889  _MotorControlInterface_Run      
0     0000591a  FS$$DIV                         
0     000059a2  _c_int00                        
0     000059f8  C$$EXIT                         
0     000059f8  _abort                          
0     000059fa  _exit                           
0     00005a21  _DelayMs                        
0     00005a3f  _InitPieVectTable               
0     00005a43  __c_int00                       
0     00005a49  __system_post_cinit             
0     00005a4a  _sqrt                           
0     00005a4a  _sqrtf                          
0     00005a70  _copy_in                        
0     00005a94  _memcpy                         
0     00005ab1  __args_main                     
0     00005aca  _DefaultIsr                     
0     00005ad4  __register_unlock               
0     00005ad8  __register_lock                 
0     00005adc  __nop                           
0     00005add  __system_pre_init               
0     00005adf  ___etext__                      
0     00005adf  etext                           
1     00000000  __stack                         
1     00000300  __STACK_END                     
1     00008000  _guiTf65Condition               
1     00008001  _guiTf910Condition              
1     00008002  _guiTf64Condition               
1     00008003  _guiTf13Condition               
1     00008004  _guiTf36Condition               
1     00008005  _guiTf53Condition               
1     00008006  _guiTf83Condition               
1     00008007  _gCurrentState                  
1     00008008  _guiTf95Condition               
1     00008009  _guiTf105Condition              
1     0000800a  _guiTf18Condition               
1     0000800b  _guiTf57Condition               
1     0000800c  _guiTf45Condition               
1     0000800d  _guiTf73Condition               
1     0000800e  _StatemachineRunToEmergencyFlag 
1     0000800f  _stcStateMachine                
1     00008013  _gstSEC                         
1     00008040  _gSTservoSimState               
1     0000808c  ___TI_enable_exit_profile_output
1     0000808e  ___TI_cleanup_ptr               
1     00008090  ___TI_dtors_ptr                 
1     00008096  __lock                          
1     00008098  __unlock                        
1     0000809a  _errno                          
abs   00000300  __STACK_SIZE                    
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
abs   ffffffff  ___pinit__                      
abs   ffffffff  binit                           
abs   ffffffff  pinit                           

[78 symbols]
