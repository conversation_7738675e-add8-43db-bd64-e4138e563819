/**
 * @file stateMachine.c
 * @brief State machine implementation for pitch control system
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "externInputInterface.h"
#include "Z_Application\StateMachine\ServoSimCore.h"

unsigned char StatemachineRunToEmergencyFlag;
STRUCT_STATE_MACHINE_INFORMATION stcStateMachine;

/* Bit manipulation macros - MISRA-C compliant */
#define GET_BIT(x, bit) (((x) & (1U << (bit))) >> (bit))

/* Bit manipulation functions with range checking - Big CamelCase naming */
ui16 SetBit(ui16 uiNum, usi8 usiBitPos) {
    ui16 uiResult = uiNum;
    if (usiBitPos < 16U) {  /* Range check for ui16 */
        uiResult = uiNum | (1U << usiBitPos);
    }
    return uiResult;
}

ui16 ClrBit(ui16 uiNum, usi8 usiBitPos) {
    ui16 uiResult = uiNum;
    if (usiBitPos < 16U) {  /* Range check for ui16 */
        uiResult = uiNum & (~(1U << usiBitPos));
    }
    return uiResult;
}

ServoSimState gSTservoSimState;
stGlobalVars gstSEC;

/* Global transition condition variables - initialized to 0 */
ui16 guiTf18Condition = 0;    /* PowerOn->Manual condition */
ui16 guiTf13Condition = 0U;    /* PowerOn->Standby condition */
ui16 guiTf36Condition = 0U;    /* Standby->PreNormal condition */
ui16 guiTf64Condition = 0U;    /* PreNormal->Normal condition */
ui16 guiTf65Condition = 0U;    /* PreNormal->VortexRun condition */
ui16 guiTf910Condition = 0U;   /* VortexRun->VortexBrake condition */
ui16 guiTf105Condition = 0U;   /* VortexBrake->Emergency condition */
ui16 guiTf45Condition = 0U;    /* Normal->Emergency condition */
ui16 guiTf57Condition = 0U;    /* Emergency->BackupTest condition */
ui16 guiTf73Condition = 0U;    /* BackupTest->Standby condition */
ui16 guiTf83Condition = 0U;    /* Manual->Standby condition */
ui16 guiTf53Condition = 0U;    /* Emergency->Standby condition */
ui16 guiTf95Condition = 0U;    /* VortexRun->Emergency condition */

/* Global current state with dual protection - CRITICAL VARIABLES */
eSystemState gCurrentState = ESTATE_POWER_ON;

/* Static function prototypes - internal use only - Big CamelCase naming */
void StateMachineInit(void);
static void HandlePowerOn(void);
static void HandleStandby(void);
static void HandlePreNormal(void);
static void HandleNormal(void);
static void HandleEmergency(void);
static void HandleBackupTest(void);
static void HandleManual(void);
static void HandleVortexRun(void);
static void HandleVortexBrake(void);

/* Transition check functions - Big CamelCase naming */
static bool CheckTf18(void);   /* PowerOn->Manual */
static bool CheckTf13(void);   /* PowerOn->Standby */
static bool CheckTf35(void);   /* Standby->Emergency */
static bool CheckTf36(void);   /* Standby->PreNormal */
static bool CheckTf64(void);   /* PreNormal->Normal */
static bool CheckTf65(void);   /* PreNormal->VortexRun */
static bool CheckTf910(void);  /* VortexRun->VortexBrake */
static bool CheckTf105(void);  /* VortexBrake->Emergency */
static bool CheckTf45(void);   /* Normal->Emergency */
static bool CheckTf57(void);   /* Emergency->BackupTest */
static bool CheckTf73(void);   /* BackupTest->Standby */
static bool CheckTf83(void);   /* Manual->Standby */
static bool CheckTf53(void);   /* Emergency->Standby */
static bool CheckTf95(void);   /* VortexRun->Emergency */

void MotorRun(ENUM_MOTION_CONTROL_MODE_SELECT Mode, unsigned int CycleTime,float TargetPosition, float TargetSpeed,float MaxSpeed, float KP1, float KP2)
{
    /* Use externInputInterface as safe abstraction layer with compile-time selection */
    MotorControlInterface_Run(Mode, CycleTime, TargetPosition, TargetSpeed, MaxSpeed, KP1, KP2);
}

void StateMachineInit(void)
{
    memset(&gstSEC,0,sizeof(gstSEC));
    /* No additional motor control initialization needed - compile-time selection is used */
}

/* Check transition conditions - max nesting depth 2 levels */
static void CheckTransitions(void) {

    switch (gCurrentState) {
        case ESTATE_POWER_ON:
            if (CheckTf18() == TRUE) {
                gCurrentState = ESTATE_MANUAL;
            } else if (CheckTf13() == TRUE) {
                gCurrentState = ESTATE_STANDBY;
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_STANDBY:
            if (CheckTf36() == TRUE) {
                gCurrentState = ESTATE_PRE_NORMAL;
            } else if (CheckTf35() == TRUE) {
                gCurrentState = ESTATE_EMERGENCY;
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_PRE_NORMAL:
            if (CheckTf64() == TRUE) {
                gCurrentState = ESTATE_NORMAL;
            } else if (CheckTf65() == TRUE) {
                gCurrentState = ESTATE_VORTEX_RUN;
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_NORMAL:
            if (CheckTf45() == TRUE) {
                gCurrentState = ESTATE_EMERGENCY;
            }
            break;

        case ESTATE_VORTEX_RUN:
            if (CheckTf910() == TRUE) {
                gCurrentState = ESTATE_VORTEX_BRAKE;
            } else if (CheckTf95() == TRUE) {
                gCurrentState = ESTATE_EMERGENCY;
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_VORTEX_BRAKE:
            if (CheckTf105() == TRUE) {
                gCurrentState = ESTATE_EMERGENCY;
            }
            break;

        case ESTATE_EMERGENCY:
            if (CheckTf57() == TRUE) {
                gCurrentState = ESTATE_BACKUP_TEST;
            } else if (CheckTf53() == TRUE) {
                gCurrentState = ESTATE_STANDBY;
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_BACKUP_TEST:
            if (CheckTf73() == TRUE) {
                gCurrentState = ESTATE_STANDBY;
            }
            break;

        case ESTATE_MANUAL:
            if (CheckTf83() == TRUE) {
                gCurrentState = ESTATE_STANDBY;
            }
            break;

        default:
            /* Invalid state - transition to emergency */
            gCurrentState = ESTATE_EMERGENCY;
            break;
    }
}

/* Main state machine processing function - max 100 lines */
void StateMachineProcess(void) {

    CheckTransitions();

    /* Execute current state processing logic */
    switch (gCurrentState) {
        case ESTATE_POWER_ON:
            HandlePowerOn();
            break;
        case ESTATE_STANDBY:
            HandleStandby();
            break;
        case ESTATE_PRE_NORMAL:
            HandlePreNormal();
            break;
        case ESTATE_NORMAL:
            HandleNormal();
            break;
        case ESTATE_EMERGENCY:
            HandleEmergency();
            break;
        case ESTATE_BACKUP_TEST:
            HandleBackupTest();
            break;
        case ESTATE_MANUAL:
            HandleManual();
            break;
        case ESTATE_VORTEX_RUN:
            HandleVortexRun();
            break;
        case ESTATE_VORTEX_BRAKE:
            HandleVortexBrake();
            break;
        default:
            /* Invalid state - force to emergency */
            gCurrentState = ESTATE_EMERGENCY;
            break;
    }
}

/* Transition check function implementations */
static bool CheckTf18(void) {
    /* PowerOn->Manual transition condition */
    if ((gstSEC.gbOthersInSafePos == TRUE) && (gstSEC.gbHandleSwitchOn == FALSE) && (gstSEC.gbManualModeEnabled == TRUE)){
        return TRUE;
    } else {
        return FALSE;
    }

}

static bool CheckTf13(void) {
    /* PowerOn->Standby transition condition */
    if ((gstSEC.gbDriverIDCheckPassed == TRUE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf35(void) {
    /* Standby->Emergency transition condition */
    if ((gstSEC.gbTCUEmModeCmd == TRUE) || (gstSEC.gbDriverErrorFeatherActive == TRUE) || (gstSEC.gbSafetyLineFb == FALSE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}
static bool CheckTf36(void) {
    /* Standby->PreNormal transition condition */
    if ((gstSEC.gbTCUNormalModeCmd == TRUE) && (gstSEC.gbDriverErrorFeatherActive == FALSE) && (gstSEC.gbSafetyLineFb == TRUE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf64(void) {

    /* PreNormal->Normal transition condition */
    if (gstSEC.gbPreNormalPosReached == TRUE) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf65(void) {
    /* PreNormal->VortexRun transition condition */
    return (GET_BIT(guiTf65Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf910(void) {
    /* VortexRun->VortexBrake transition condition */
    return (GET_BIT(guiTf910Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf105(void) {
    /* VortexBrake->Emergency transition condition */
    return (GET_BIT(guiTf105Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf45(void) {
    /* Normal->Emergency transition condition */
    if ( gstSEC.gbDriverErrorFeatherActive == TRUE) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf57(void) {
    /* Emergency->BackupTest transition condition */
    return (GET_BIT(guiTf57Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf73(void) {
    /* BackupTest->Standby transition condition */
    return (GET_BIT(guiTf73Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf83(void) {
    /* Manual->Standby transition condition */
    return (GET_BIT(guiTf83Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf53(void) {
    /* Emergency->Standby transition condition */
    return (GET_BIT(guiTf53Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf95(void) {
    /* VortexRun->Emergency transition condition */
    return (GET_BIT(guiTf95Condition, 0U) == 1U) ? TRUE : FALSE;
}


/* State handling function implementations */
static void HandlePowerOn(void) {

    if (gstSEC.gbDriverIDCheckPassed == FALSE){
        gstSEC.gbDriverIDCheckPassed = TRUE;
    }

    gstSEC.guiGearRatioAll = 3000U;
    /* Initialize system hardware */
    /* Add specific power-on logic here */
}

static void HandleStandby(void) {

    /* Standby mode logic */
    /* Add specific standby logic here */

}

static void HandlePreNormal(void) {

    gstSEC.giCommandMotorPosition = 8900;
    gstSEC.giCommandMotorSpeedLimit = 500;

    MotorRun(MOTION_CONTROL_MODE_POSITION,0,gstSEC.giCommandMotorPosition,gstSEC.giCommandMotorSpeedLimit,gstSEC.giCommandMotorSpeedLimit,0,0);

    if ((gstSEC.giMotorPosition > 8850)&&(gstSEC.giMotorPosition < 8950)) {
        gstSEC.gbPreNormalPosReached = TRUE;
    }

}

static void HandleNormal(void) {

    gstSEC.giCommandMotorPosition = 7000;
    gstSEC.giCommandMotorSpeedLimit = 500;

    MotorRun(MOTION_CONTROL_MODE_POSITION,0,gstSEC.giCommandMotorPosition,gstSEC.giCommandMotorSpeedLimit,gstSEC.giCommandMotorSpeedLimit,0,0);

    if ((gstSEC.giMotorPosition > 6950)&&(gstSEC.giMotorPosition < 7050)) {
        gstSEC.gbDriverErrorFeatherActive = TRUE;
    }

    /* Normal operation logic */
    /* Add specific normal operation logic here */
}

static void HandleEmergency(void) {

    gstSEC.giCommandMotorPosition = 9100;
    gstSEC.giCommandMotorSpeedLimit = 500;

    MotorRun(MOTION_CONTROL_MODE_SPEED,0,gstSEC.giCommandMotorPosition,gstSEC.giCommandMotorSpeedLimit,gstSEC.giCommandMotorSpeedLimit,0,0);

    if ((gstSEC.giMotorPosition > 9095)&&(gstSEC.giMotorPosition < 9105)) {
        MotorRun(MOTION_CONTROL_MODE_SPEED,0,gstSEC.giCommandMotorPosition,0,gstSEC.giCommandMotorSpeedLimit,0,0);
    }

    /* Emergency handling logic */
    /* Add specific emergency logic here */
}

static void HandleBackupTest(void) {

    /* Backup test logic */
    /* Add specific backup test logic here */
}

static void HandleManual(void) {

    /* Manual mode logic */
    /* Add specific manual mode logic here */
}

static void HandleVortexRun(void) {

    /* Vortex run mode logic */
    /* Add specific vortex run logic here */
}

static void HandleVortexBrake(void) {

    /* Vortex brake mode logic */
    /* Add specific vortex brake logic here */
}

