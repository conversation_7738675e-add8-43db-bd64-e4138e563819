/**
 * @file ServoSimCore.h
 * @brief Servo simulation core header
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef SERVO_SIM_CORE_H
#define SERVO_SIM_CORE_H

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _WIN32
#define EXPORT __declspec(dllexport)
#else
#define EXPORT
#endif

typedef struct {
    /* Input parameters */
    double dAcVoltage;        /* AC power voltage (V) */
    double dPositionCommand;  /* Position command (deg) */
    double dSpeedLimit;       /* Speed limit (deg/s) */
    double dAccelerationLimit;/* Acceleration limit (deg/s^2) */
    double dLoadTorque;       /* Load torque (N*m) */
    double dMotionSpeedCommand;  /* Speed command (deg) for speed mode */

    /* Output parameters */
    double dMotorCurrentD;    /* Motor d-axis current (A) */
    double dMotorCurrentQ;    /* Motor q-axis current (A) */
    double dMotorVoltageD;    /* Motor d-axis voltage (V) */
    double dMotorVoltageQ;    /* Motor q-axis voltage (V) */
    double dMotorSpeed;       /* Motor speed (deg/s) */
    double dMotorPosition;    /* Motor position (deg) */

    /* Control parameters */
    double dPositionKp;       /* Position loop proportional gain */
    double dPositionKi;       /* Position loop integral gain */
    double dSpeedKp;          /* Speed loop proportional gain */
    double dSpeedKi;          /* Speed loop integral gain */
    double dCurrentKp;        /* Current loop proportional gain */
    double dCurrentKi;        /* Current loop integral gain */

    /* Motor parameters */
    double dMotorRs;          /* Stator resistance (Ohm) */
    double dMotorLd;          /* d-axis inductance (H) */
    double dMotorLq;          /* q-axis inductance (H) */
    double dMotorInertia;     /* Rotor inertia (kg*m^2) */
    double dMotorDamping;     /* Damping coefficient */
    double dMotorPoles;       /* Pole pairs */
    double dMotorFlux;        /* Permanent magnet flux (Wb) */

    /* Internal state variables */
    double dPositionError;    /* Position error */
    double dSpeedError;       /* Speed error */
    double dCurrentErrorD;    /* d-axis current error */
    double dCurrentErrorQ;    /* q-axis current error */
    double dPositionIntegral; /* Position integral */
    double dSpeedIntegral;    /* Speed integral */
    double dCurrentIntegralD; /* d-axis current integral */
    double dCurrentIntegralQ; /* q-axis current integral */
    double dElectricalAngle;  /* Electrical angle (deg) */

    /* Simulation control */
    int iRunning;             /* Simulation running flag */
    double dTimeStep;         /* Simulation time step (s) */
    double dSimulationTime;   /* Current simulation time (s) */
    int iRealTimeUpdate;      /* Real-time update flag */
    int iModeControl;      /* 0: positon mode; 1: speed mode */
} ServoSimState;

/* Function prototypes - Big CamelCase naming */
EXPORT void InitSimulation(ServoSimState* pState);

EXPORT void SimulationStep(ServoSimState* pState);

EXPORT void ResetSimulation(ServoSimState* pState);

EXPORT double DegToRad(double dDeg);

EXPORT double RadToDeg(double dRad);

#ifdef __cplusplus
}
#endif

#endif /* SERVO_SIM_CORE_H */

