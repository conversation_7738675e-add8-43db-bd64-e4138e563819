<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v18.1.4.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x688b0e6c</link_time>
   <link_errors>0x0</link_errors>
   <output_file>testStateMachineAugment_safety.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x5900</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\</path>
         <kind>object</kind>
         <file>CodeStartBranch.obj</file>
         <name>CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\</path>
         <kind>object</kind>
         <file>ServoSimCore.obj</file>
         <name>ServoSimCore.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\</path>
         <kind>object</kind>
         <file>externInputInterface.obj</file>
         <name>externInputInterface.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\</path>
         <kind>object</kind>
         <file>stateMachine.obj</file>
         <name>stateMachine.obj</name>
      </input_file>
      <input_file id="fl-a">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>e_fmodf.c.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>e_sqrtf.c.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fs_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-1d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-1e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>errno.c.obj</name>
      </input_file>
      <input_file id="fl-1f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-20">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-2c">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x69</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-61">
         <name>.cinit</name>
         <load_address>0x4069</load_address>
         <run_address>0x4069</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-24">
         <name>.cinit</name>
         <load_address>0x4077</load_address>
         <run_address>0x4077</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-79">
         <name>.cinit:__lock</name>
         <load_address>0x4081</load_address>
         <run_address>0x4081</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.cinit:__unlock</name>
         <load_address>0x4086</load_address>
         <run_address>0x4086</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.cinit</name>
         <load_address>0x408b</load_address>
         <run_address>0x408b</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.cinit</name>
         <load_address>0x408f</load_address>
         <run_address>0x408f</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x554</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text</name>
         <load_address>0x5554</load_address>
         <run_address>0x5554</run_address>
         <size>0x256</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text</name>
         <load_address>0x57aa</load_address>
         <run_address>0x57aa</run_address>
         <size>0xce</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text</name>
         <load_address>0x5878</load_address>
         <run_address>0x5878</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-11">
         <name>.text</name>
         <load_address>0x5900</load_address>
         <run_address>0x5900</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-75">
         <name>.text</name>
         <load_address>0x5956</load_address>
         <run_address>0x5956</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.text</name>
         <load_address>0x597f</load_address>
         <run_address>0x597f</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text</name>
         <load_address>0x59a7</load_address>
         <run_address>0x59a7</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text</name>
         <load_address>0x59a8</load_address>
         <run_address>0x59a8</run_address>
         <size>0x26</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text</name>
         <load_address>0x59ce</load_address>
         <run_address>0x59ce</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-76">
         <name>.text</name>
         <load_address>0x59f2</load_address>
         <run_address>0x59f2</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text</name>
         <load_address>0x5a0f</load_address>
         <run_address>0x5a0f</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-26">
         <name>.text:retain</name>
         <load_address>0x5a28</load_address>
         <run_address>0x5a28</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text</name>
         <load_address>0x5a32</load_address>
         <run_address>0x5a32</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text</name>
         <load_address>0x5a3b</load_address>
         <run_address>0x5a3b</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-12">
         <name>codestart</name>
         <load_address>0x40ca</load_address>
         <run_address>0x40ca</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-77">
         <name>ramfuncs</name>
         <load_address>0x4095</load_address>
         <run_address>0x4095</run_address>
         <size>0x35</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-1e">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80d8</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-25">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80d0</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8000</run_address>
         <size>0xca</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80d9</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-62">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80ca</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80d6</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-78">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x80d4</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-82">
         <name>.econst:_Zero</name>
         <load_address>0x80da</load_address>
         <run_address>0x80da</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-33">
         <name>.econst</name>
         <load_address>0x80de</load_address>
         <run_address>0x80de</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf2</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17">
         <name>.debug_info</name>
         <load_address>0xf2</load_address>
         <run_address>0xf2</run_address>
         <size>0x1797</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.debug_info</name>
         <load_address>0x1889</load_address>
         <run_address>0x1889</run_address>
         <size>0x60c</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x1e95</load_address>
         <run_address>0x1e95</run_address>
         <size>0x630</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.debug_info</name>
         <load_address>0x24c5</load_address>
         <run_address>0x24c5</run_address>
         <size>0x25d5</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_info</name>
         <load_address>0x4a9a</load_address>
         <run_address>0x4a9a</run_address>
         <size>0x4f8</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x4f92</load_address>
         <run_address>0x4f92</run_address>
         <size>0x426</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_info</name>
         <load_address>0x53b8</load_address>
         <run_address>0x53b8</run_address>
         <size>0x176</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_info</name>
         <load_address>0x552e</load_address>
         <run_address>0x552e</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x5645</load_address>
         <run_address>0x5645</run_address>
         <size>0x58a</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x5bcf</load_address>
         <run_address>0x5bcf</run_address>
         <size>0x485</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_info</name>
         <load_address>0x6054</load_address>
         <run_address>0x6054</run_address>
         <size>0x3f8</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_info</name>
         <load_address>0x644c</load_address>
         <run_address>0x644c</run_address>
         <size>0x3f5</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_info</name>
         <load_address>0x6841</load_address>
         <run_address>0x6841</run_address>
         <size>0x1c6</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x6a07</load_address>
         <run_address>0x6a07</run_address>
         <size>0x58f</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_info</name>
         <load_address>0x6f96</load_address>
         <run_address>0x6f96</run_address>
         <size>0x517</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_info</name>
         <load_address>0x74ad</load_address>
         <run_address>0x74ad</run_address>
         <size>0x4b2</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x795f</load_address>
         <run_address>0x795f</run_address>
         <size>0xb8</size>
      </object_component>
      <object_component id="oc-14">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_line</name>
         <load_address>0x45</load_address>
         <run_address>0x45</run_address>
         <size>0x290</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.debug_line</name>
         <load_address>0x2d5</load_address>
         <run_address>0x2d5</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-28">
         <name>.debug_line</name>
         <load_address>0x30f</load_address>
         <run_address>0x30f</run_address>
         <size>0x105</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_line</name>
         <load_address>0x414</load_address>
         <run_address>0x414</run_address>
         <size>0x42e</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x842</load_address>
         <run_address>0x842</run_address>
         <size>0xb7</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_line</name>
         <load_address>0x8f9</load_address>
         <run_address>0x8f9</run_address>
         <size>0x57</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_line</name>
         <load_address>0x950</load_address>
         <run_address>0x950</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x9ce</load_address>
         <run_address>0x9ce</run_address>
         <size>0xad</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0xa7b</load_address>
         <run_address>0xa7b</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_line</name>
         <load_address>0xacb</load_address>
         <run_address>0xacb</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_line</name>
         <load_address>0xb2d</load_address>
         <run_address>0xb2d</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_line</name>
         <load_address>0xb6b</load_address>
         <run_address>0xb6b</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0xba5</load_address>
         <run_address>0xba5</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_line</name>
         <load_address>0xc0a</load_address>
         <run_address>0xc0a</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_line</name>
         <load_address>0xc68</load_address>
         <run_address>0xc68</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-15">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.debug_abbrev</name>
         <load_address>0x21</load_address>
         <run_address>0x21</run_address>
         <size>0x16d</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_abbrev</name>
         <load_address>0x18e</load_address>
         <run_address>0x18e</run_address>
         <size>0xb3</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-29">
         <name>.debug_abbrev</name>
         <load_address>0x241</load_address>
         <run_address>0x241</run_address>
         <size>0xf7</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_abbrev</name>
         <load_address>0x338</load_address>
         <run_address>0x338</run_address>
         <size>0x1b2</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x4ea</load_address>
         <run_address>0x4ea</run_address>
         <size>0xc1</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x5ab</load_address>
         <run_address>0x5ab</run_address>
         <size>0xa4</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x64f</load_address>
         <run_address>0x64f</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_abbrev</name>
         <load_address>0x695</load_address>
         <run_address>0x695</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_abbrev</name>
         <load_address>0x6cd</load_address>
         <run_address>0x6cd</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_abbrev</name>
         <load_address>0x858</load_address>
         <run_address>0x858</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_abbrev</name>
         <load_address>0x8c0</load_address>
         <run_address>0x8c0</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_abbrev</name>
         <load_address>0x926</load_address>
         <run_address>0x926</run_address>
         <size>0x31</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_abbrev</name>
         <load_address>0x957</load_address>
         <run_address>0x957</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_abbrev</name>
         <load_address>0xa87</load_address>
         <run_address>0xa87</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_abbrev</name>
         <load_address>0xb39</load_address>
         <run_address>0xb39</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_abbrev</name>
         <load_address>0xc27</load_address>
         <run_address>0xc27</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-16">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x90</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_aranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2a">
         <name>.debug_aranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_aranges</name>
         <load_address>0x110</load_address>
         <run_address>0x110</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_aranges</name>
         <load_address>0x210</load_address>
         <run_address>0x210</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_aranges</name>
         <load_address>0x230</load_address>
         <run_address>0x230</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_aranges</name>
         <load_address>0x250</load_address>
         <run_address>0x250</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x270</load_address>
         <run_address>0x270</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_aranges</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_aranges</name>
         <load_address>0x2b0</load_address>
         <run_address>0x2b0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_aranges</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_aranges</name>
         <load_address>0x2f0</load_address>
         <run_address>0x2f0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_aranges</name>
         <load_address>0x310</load_address>
         <run_address>0x310</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_aranges</name>
         <load_address>0x338</load_address>
         <run_address>0x338</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_aranges</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-18">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e4</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x1e4</load_address>
         <run_address>0x1e4</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-27">
         <name>.debug_frame</name>
         <load_address>0x214</load_address>
         <run_address>0x214</run_address>
         <size>0xd4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2e">
         <name>.debug_frame</name>
         <load_address>0x2e8</load_address>
         <run_address>0x2e8</run_address>
         <size>0x2fc</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_frame</name>
         <load_address>0x5e4</load_address>
         <run_address>0x5e4</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_frame</name>
         <load_address>0x634</load_address>
         <run_address>0x634</run_address>
         <size>0x4c</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_frame</name>
         <load_address>0x680</load_address>
         <run_address>0x680</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x6e8</load_address>
         <run_address>0x6e8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x730</load_address>
         <run_address>0x730</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0x778</load_address>
         <run_address>0x778</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x7c0</load_address>
         <run_address>0x7c0</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_frame</name>
         <load_address>0x828</load_address>
         <run_address>0x828</run_address>
         <size>0x78</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_frame</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x95</size>
         <contents>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-5d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0xa3d</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-11"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-26"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-71"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x40ca</load_address>
         <run_address>0x40ca</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-12"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x4095</load_address>
         <run_address>0x4095</run_address>
         <size>0x35</size>
         <contents>
            <object_component_ref idref="oc-77"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-3e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>vectors</name>
         <run_address>0x3fffc2</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x0</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-9d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0x8000</run_address>
         <size>0xda</size>
         <contents>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-78"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0x80da</load_address>
         <run_address>0x80da</run_address>
         <size>0x6</size>
         <contents>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-33"/>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-91" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7a17</size>
         <contents>
            <object_component_ref idref="oc-13"/>
            <object_component_ref idref="oc-17"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-9e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-93" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcbd</size>
         <contents>
            <object_component_ref idref="oc-14"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-28"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-95" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc36</size>
         <contents>
            <object_component_ref idref="oc-15"/>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-29"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-9f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-97" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x388</size>
         <contents>
            <object_component_ref idref="oc-16"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-2a"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-70"/>
         </contents>
      </logical_group>
      <logical_group id="lg-99" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8e8</size>
         <contents>
            <object_component_ref idref="oc-18"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-27"/>
            <object_component_ref idref="oc-2e"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6d"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>ZONE0</name>
         <page_id>0x0</page_id>
         <origin>0x4000</origin>
         <length>0x1000</length>
         <used_space>0xcc</used_space>
         <unused_space>0xf34</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4000</start_address>
               <size>0x95</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4095</start_address>
               <size>0x35</size>
               <logical_group_ref idref="lg-6"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x40ca</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x40cc</start_address>
               <size>0xf34</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE1</name>
         <page_id>0x0</page_id>
         <origin>0x5000</origin>
         <length>0x1000</length>
         <used_space>0xa3d</used_space>
         <unused_space>0x5c3</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0xa3d</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x5a3d</start_address>
               <size>0x5c3</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE2</name>
         <page_id>0x0</page_id>
         <origin>0x6000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE3</name>
         <page_id>0x0</page_id>
         <origin>0x7000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE4</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE5</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x0</used_space>
         <unused_space>0x9</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>VECTORS</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc2</origin>
         <length>0x3e</length>
         <used_space>0x0</used_space>
         <unused_space>0x3e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x0</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0x300</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x0</used_space>
         <unused_space>0x400</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIEDATA</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x0</used_space>
         <unused_space>0x20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0xe0</used_space>
         <unused_space>0xf20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0xda</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x80da</start_address>
               <size>0x6</size>
               <logical_group_ref idref="lg-c"/>
            </allocated_space>
            <available_space>
               <start_address>0x80e0</start_address>
               <size>0xf20</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L2SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L3SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0x5a3d</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0x5a3d</value>
      </symbol>
      <symbol id="sm-d">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-41">
         <name>code_start</name>
         <value>0x40ca</value>
         <object_component_ref idref="oc-12"/>
      </symbol>
      <symbol id="sm-69">
         <name>_DegToRad</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-6a">
         <name>_SimulationStep</name>
         <value>0x54d2</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-6b">
         <name>_ResetSimulation</name>
         <value>0x54ff</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-6c">
         <name>_InitSimulation</name>
         <value>0x5124</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-6d">
         <name>_RadToDeg</name>
         <value>0x5011</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-9a">
         <name>_main</name>
         <value>0x40aa</value>
         <object_component_ref idref="oc-77"/>
      </symbol>
      <symbol id="sm-9b">
         <name>_InitSystem</name>
         <value>0x4095</value>
         <object_component_ref idref="oc-77"/>
      </symbol>
      <symbol id="sm-9c">
         <name>_DefaultIsr</name>
         <value>0x5a28</value>
         <object_component_ref idref="oc-26"/>
      </symbol>
      <symbol id="sm-9d">
         <name>__c_int00</name>
         <value>0x59a1</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-9e">
         <name>_DelayMs</name>
         <value>0x597f</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-9f">
         <name>_InitPieVectTable</name>
         <value>0x599d</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-113">
         <name>_guiTf910Condition</name>
         <value>0x8003</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-114">
         <name>_guiManualCnt</name>
         <value>0x8010</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-115">
         <name>_SetBit</name>
         <value>0x5554</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-116">
         <name>_guiTf36Condition</name>
         <value>0x8006</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-117">
         <name>_ClrBit</name>
         <value>0x5563</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-118">
         <name>_guiTf18Condition</name>
         <value>0x8004</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-119">
         <name>_guiPowerOnCnt</name>
         <value>0x8018</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11a">
         <name>_guiPreNormalCnt</name>
         <value>0x801a</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11b">
         <name>_guiTf64Condition</name>
         <value>0x8001</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11c">
         <name>_gSTservoSimState</name>
         <value>0x8080</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11d">
         <name>_guiEmergencyCnt</name>
         <value>0x801c</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11e">
         <name>_guiTf13Condition</name>
         <value>0x800c</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-11f">
         <name>_guiTf73Condition</name>
         <value>0x800d</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-120">
         <name>_guiTf65Condition</name>
         <value>0x8000</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-121">
         <name>_guiTf53Condition</name>
         <value>0x8007</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-122">
         <name>_guiTf57Condition</name>
         <value>0x800e</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-123">
         <name>_guiTf45Condition</name>
         <value>0x800b</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-124">
         <name>_guiTf95Condition</name>
         <value>0x800a</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-125">
         <name>_guiNormalCnt</name>
         <value>0x801e</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-126">
         <name>_guiTf83Condition</name>
         <value>0x8008</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-127">
         <name>_guiVortexRunCnt</name>
         <value>0x8020</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-128">
         <name>_gCurrentStateBak</name>
         <value>0x800f</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-129">
         <name>_guiVortexBrakeCnt</name>
         <value>0x8014</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-12a">
         <name>_guiBackupTestCnt</name>
         <value>0x8012</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-12b">
         <name>_gCurrentState</name>
         <value>0x8009</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-12c">
         <name>_StateMachineProcess</name>
         <value>0x561f</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-12d">
         <name>_gstSEC</name>
         <value>0x8040</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-12e">
         <name>_guiStandbyCnt</name>
         <value>0x8016</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-12f">
         <name>_GetCurrentState</name>
         <value>0x5573</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-130">
         <name>_eCurrentState</name>
         <value>0x8005</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-131">
         <name>_guiTf105Condition</name>
         <value>0x8002</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-15d">
         <name>_fmodf</name>
         <value>0x57aa</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-15e">
         <name>_fmod</name>
         <value>0x57aa</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-16e">
         <name>_sqrtf</name>
         <value>0x59a8</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-16f">
         <name>_sqrt</name>
         <value>0x59a8</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-18a">
         <name>_c_int00</name>
         <value>0x5900</value>
         <object_component_ref idref="oc-11"/>
      </symbol>
      <symbol id="sm-18b">
         <name>__stack</name>
         <value>0x0</value>
         <object_component_ref idref="oc-3d"/>
      </symbol>
      <symbol id="sm-19c">
         <name>FS$$DIV</name>
         <value>0x5878</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-1ab">
         <name>_copy_in</name>
         <value>0x59ce</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>_memcpy</name>
         <value>0x59f2</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>__system_pre_init</name>
         <value>0x5a3b</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__system_post_cinit</name>
         <value>0x59a7</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1df">
         <name>_errno</name>
         <value>0x80d9</value>
         <object_component_ref idref="oc-5e"/>
      </symbol>
      <symbol id="sm-1f3">
         <name>C$$EXIT</name>
         <value>0x5956</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>_exit</name>
         <value>0x5958</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>___TI_cleanup_ptr</name>
         <value>0x80cc</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>___TI_enable_exit_profile_output</name>
         <value>0x80ca</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>_abort</name>
         <value>0x5956</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>___TI_dtors_ptr</name>
         <value>0x80ce</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-20c">
         <name>__unlock</name>
         <value>0x80d6</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-20d">
         <name>__lock</name>
         <value>0x80d4</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-20e">
         <name>__register_lock</name>
         <value>0x5a36</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-20f">
         <name>__nop</name>
         <value>0x5a3a</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-210">
         <name>__register_unlock</name>
         <value>0x5a32</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-21f">
         <name>__args_main</name>
         <value>0x5a0f</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
