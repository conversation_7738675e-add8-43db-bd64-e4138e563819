******************************************************************************
             TMS320C2000 Linker PC v18.1.4                     
******************************************************************************
>> Linked Thu Jul 31 14:34:20 2025

OUTPUT FILE NAME:   <testStateMachineAugment_safety.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 00005900


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  ZONE0                 00004000   00001000  000000cc  00000f34  RWIX
  ZONE1                 00005000   00001000  00000a3d  000005c3  RWIX
  ZONE2                 00006000   00001000  00000000  00001000  RWIX
  ZONE3                 00007000   00001000  00000000  00001000  RWIX
  ZONE4                 00008000   00001000  00000000  00001000  RWIX
  ZONE5                 00009000   00001000  00000000  00001000  RWIX
  ZONE6                 0000a000   00001000  00000000  00001000  RWIX
  ZONE7                 0000b000   00001000  00000000  00001000  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000000  00000009  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX
  VECTORS               003fffc2   0000003e  00000000  0000003e  RWIX

PAGE 1:
  M0SARAM               00000000   00000400  00000300  00000100  RWIX
  M1SARAM               00000400   00000400  00000000  00000400  RWIX
  PIEDATA               00000ce0   00000020  00000000  00000020  RWIX
  L0SARAM               00008000   00001000  000000e0  00000f20  RWIX
  L1SARAM               00009000   00001000  00000000  00001000  RWIX
  L2SARAM               0000a000   00001000  00000000  00001000  RWIX
  L3SARAM               0000b000   00001000  00000000  00001000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.cinit     0    00004000    00000095     
                  00004000    00000069     stateMachine.obj (.cinit)
                  00004069    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  00004077    0000000a     main.obj (.cinit)
                  00004081    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  00004086    00000005                       : _lock.c.obj (.cinit:__unlock)
                  0000408b    00000004     externInputInterface.obj (.cinit)
                  0000408f    00000004     rts2800_fpu32.lib : errno.c.obj (.cinit)
                  00004093    00000002     --HOLE-- [fill = 0]

.pinit     0    00004000    00000000     UNINITIALIZED

ramfuncs   0    00004095    00000035     
                  00004095    00000035     main.obj (ramfuncs)

codestart 
*          0    000040ca    00000002     
                  000040ca    00000002     CodeStartBranch.obj (codestart)

.text      0    00005000    00000a3d     
                  00005000    00000554     ServoSimCore.obj (.text)
                  00005554    00000256     stateMachine.obj (.text)
                  000057aa    000000ce     rts2800_fpu32.lib : e_fmodf.c.obj (.text)
                  00005878    00000088                       : fs_div28.asm.obj (.text)
                  00005900    00000056                       : boot28.asm.obj (.text)
                  00005956    00000029                       : exit.c.obj (.text)
                  0000597f    00000028     main.obj (.text)
                  000059a7    00000001     rts2800_fpu32.lib : startup.c.obj (.text)
                  000059a8    00000026                       : e_sqrtf.c.obj (.text)
                  000059ce    00000024                       : cpy_tbl.c.obj (.text)
                  000059f2    0000001d                       : memcpy.c.obj (.text)
                  00005a0f    00000019                       : args_main.c.obj (.text)
                  00005a28    0000000a     main.obj (.text:retain)
                  00005a32    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  00005a3b    00000002                       : pre_init.c.obj (.text)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot28.asm.obj (.reset)

vectors    0    003fffc2    00000000     DSECT

.stack     1    00000000    00000300     UNINITIALIZED
                  00000000    00000300     --HOLE--

.ebss      1    00008000    000000da     UNINITIALIZED
                  00008000    000000ca     stateMachine.obj (.ebss)
                  000080ca    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  000080d0    00000004     main.obj (.ebss)
                  000080d4    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  000080d6    00000002                       : _lock.c.obj (.ebss:__unlock)
                  000080d8    00000001     externInputInterface.obj (.ebss)
                  000080d9    00000001     rts2800_fpu32.lib : errno.c.obj (.ebss)

.econst    1    000080da    00000006     
                  000080da    00000004     rts2800_fpu32.lib : e_fmodf.c.obj (.econst:_Zero)
                  000080de    00000002                       : e_fmodf.c.obj (.econst)

MODULE SUMMARY

       Module                     code   initialized data   uninitialized data
       ------                     ----   ----------------   ------------------
    .\
       ServoSimCore.obj           1364   0                  0                 
       stateMachine.obj           598    105                202               
       main.obj                   103    10                 4                 
       externInputInterface.obj   0      4                  1                 
       CodeStartBranch.obj        2      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     2067   119                207               
                                                                              
    D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\rts2800_fpu32.lib
       e_fmodf.c.obj              206    6                  0                 
       fs_div28.asm.obj           136    0                  0                 
       boot28.asm.obj             86     0                  0                 
       exit.c.obj                 41     14                 6                 
       e_sqrtf.c.obj              38     0                  0                 
       cpy_tbl.c.obj              36     0                  0                 
       memcpy.c.obj               29     0                  0                 
       args_main.c.obj            25     0                  0                 
       _lock.c.obj                9      10                 4                 
       errno.c.obj                0      4                  1                 
       pre_init.c.obj             2      0                  0                 
       startup.c.obj              1      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     609    34                 11                
                                                                              
       Stack:                     0      0                  768               
    +--+--------------------------+------+------------------+--------------------+
       Grand Total:               2676   153                986               


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000000       0 (00000000)     __stack

00008000     200 (00008000)     _guiTf65Condition
00008001     200 (00008000)     _guiTf64Condition
00008002     200 (00008000)     _guiTf105Condition
00008003     200 (00008000)     _guiTf910Condition
00008004     200 (00008000)     _guiTf18Condition
00008005     200 (00008000)     _eCurrentState
00008006     200 (00008000)     _guiTf36Condition
00008007     200 (00008000)     _guiTf53Condition
00008008     200 (00008000)     _guiTf83Condition
00008009     200 (00008000)     _gCurrentState
0000800a     200 (00008000)     _guiTf95Condition
0000800b     200 (00008000)     _guiTf45Condition
0000800c     200 (00008000)     _guiTf13Condition
0000800d     200 (00008000)     _guiTf73Condition
0000800e     200 (00008000)     _guiTf57Condition
0000800f     200 (00008000)     _gCurrentStateBak
00008010     200 (00008000)     _guiManualCnt
00008012     200 (00008000)     _guiBackupTestCnt
00008014     200 (00008000)     _guiVortexBrakeCnt
00008016     200 (00008000)     _guiStandbyCnt
00008018     200 (00008000)     _guiPowerOnCnt
0000801a     200 (00008000)     _guiPreNormalCnt
0000801c     200 (00008000)     _guiEmergencyCnt
0000801e     200 (00008000)     _guiNormalCnt
00008020     200 (00008000)     _guiVortexRunCnt

00008040     201 (00008040)     _gstSEC

00008080     202 (00008080)     _gSTservoSimState

000080ca     203 (000080c0)     ___TI_enable_exit_profile_output
000080cc     203 (000080c0)     ___TI_cleanup_ptr
000080ce     203 (000080c0)     ___TI_dtors_ptr
000080d4     203 (000080c0)     __lock
000080d6     203 (000080c0)     __unlock
000080d9     203 (000080c0)     _errno


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                            
----  -------   ----                            
0     00005000  .text                           
0     00005956  C$$EXIT                         
0     00005878  FS$$DIV                         
0     00005563  _ClrBit                         
0     00005a28  _DefaultIsr                     
0     00005000  _DegToRad                       
0     0000597f  _DelayMs                        
0     00005573  _GetCurrentState                
0     0000599d  _InitPieVectTable               
0     00005124  _InitSimulation                 
0     00004095  _InitSystem                     
0     00005011  _RadToDeg                       
0     000054ff  _ResetSimulation                
0     00005554  _SetBit                         
0     000054d2  _SimulationStep                 
0     0000561f  _StateMachineProcess            
1     00000300  __STACK_END                     
abs   00000300  __STACK_SIZE                    
1     000080cc  ___TI_cleanup_ptr               
1     000080ce  ___TI_dtors_ptr                 
1     000080ca  ___TI_enable_exit_profile_output
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
0     00004000  ___cinit__                      
0     00005a3d  ___etext__                      
abs   ffffffff  ___pinit__                      
0     00005000  ___text__                       
0     00005a0f  __args_main                     
0     000059a1  __c_int00                       
1     000080d4  __lock                          
0     00005a3a  __nop                           
0     00005a36  __register_lock                 
0     00005a32  __register_unlock               
1     00000000  __stack                         
0     000059a7  __system_post_cinit             
0     00005a3b  __system_pre_init               
1     000080d6  __unlock                        
0     00005956  _abort                          
0     00005900  _c_int00                        
0     000059ce  _copy_in                        
1     00008005  _eCurrentState                  
1     000080d9  _errno                          
0     00005958  _exit                           
0     000057aa  _fmod                           
0     000057aa  _fmodf                          
1     00008009  _gCurrentState                  
1     0000800f  _gCurrentStateBak               
1     00008080  _gSTservoSimState               
1     00008040  _gstSEC                         
1     00008012  _guiBackupTestCnt               
1     0000801c  _guiEmergencyCnt                
1     00008010  _guiManualCnt                   
1     0000801e  _guiNormalCnt                   
1     00008018  _guiPowerOnCnt                  
1     0000801a  _guiPreNormalCnt                
1     00008016  _guiStandbyCnt                  
1     00008002  _guiTf105Condition              
1     0000800c  _guiTf13Condition               
1     00008004  _guiTf18Condition               
1     00008006  _guiTf36Condition               
1     0000800b  _guiTf45Condition               
1     00008007  _guiTf53Condition               
1     0000800e  _guiTf57Condition               
1     00008001  _guiTf64Condition               
1     00008000  _guiTf65Condition               
1     0000800d  _guiTf73Condition               
1     00008008  _guiTf83Condition               
1     00008003  _guiTf910Condition              
1     0000800a  _guiTf95Condition               
1     00008014  _guiVortexBrakeCnt              
1     00008020  _guiVortexRunCnt                
0     000040aa  _main                           
0     000059f2  _memcpy                         
0     000059a8  _sqrt                           
0     000059a8  _sqrtf                          
abs   ffffffff  binit                           
0     00004000  cinit                           
0     000040ca  code_start                      
0     00005a3d  etext                           
abs   ffffffff  pinit                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                            
----  -------   ----                            
0     00004000  ___cinit__                      
0     00004000  cinit                           
0     00004095  _InitSystem                     
0     000040aa  _main                           
0     000040ca  code_start                      
0     00005000  .text                           
0     00005000  _DegToRad                       
0     00005000  ___text__                       
0     00005011  _RadToDeg                       
0     00005124  _InitSimulation                 
0     000054d2  _SimulationStep                 
0     000054ff  _ResetSimulation                
0     00005554  _SetBit                         
0     00005563  _ClrBit                         
0     00005573  _GetCurrentState                
0     0000561f  _StateMachineProcess            
0     000057aa  _fmod                           
0     000057aa  _fmodf                          
0     00005878  FS$$DIV                         
0     00005900  _c_int00                        
0     00005956  C$$EXIT                         
0     00005956  _abort                          
0     00005958  _exit                           
0     0000597f  _DelayMs                        
0     0000599d  _InitPieVectTable               
0     000059a1  __c_int00                       
0     000059a7  __system_post_cinit             
0     000059a8  _sqrt                           
0     000059a8  _sqrtf                          
0     000059ce  _copy_in                        
0     000059f2  _memcpy                         
0     00005a0f  __args_main                     
0     00005a28  _DefaultIsr                     
0     00005a32  __register_unlock               
0     00005a36  __register_lock                 
0     00005a3a  __nop                           
0     00005a3b  __system_pre_init               
0     00005a3d  ___etext__                      
0     00005a3d  etext                           
1     00000000  __stack                         
1     00000300  __STACK_END                     
1     00008000  _guiTf65Condition               
1     00008001  _guiTf64Condition               
1     00008002  _guiTf105Condition              
1     00008003  _guiTf910Condition              
1     00008004  _guiTf18Condition               
1     00008005  _eCurrentState                  
1     00008006  _guiTf36Condition               
1     00008007  _guiTf53Condition               
1     00008008  _guiTf83Condition               
1     00008009  _gCurrentState                  
1     0000800a  _guiTf95Condition               
1     0000800b  _guiTf45Condition               
1     0000800c  _guiTf13Condition               
1     0000800d  _guiTf73Condition               
1     0000800e  _guiTf57Condition               
1     0000800f  _gCurrentStateBak               
1     00008010  _guiManualCnt                   
1     00008012  _guiBackupTestCnt               
1     00008014  _guiVortexBrakeCnt              
1     00008016  _guiStandbyCnt                  
1     00008018  _guiPowerOnCnt                  
1     0000801a  _guiPreNormalCnt                
1     0000801c  _guiEmergencyCnt                
1     0000801e  _guiNormalCnt                   
1     00008020  _guiVortexRunCnt                
1     00008040  _gstSEC                         
1     00008080  _gSTservoSimState               
1     000080ca  ___TI_enable_exit_profile_output
1     000080cc  ___TI_cleanup_ptr               
1     000080ce  ___TI_dtors_ptr                 
1     000080d4  __lock                          
1     000080d6  __unlock                        
1     000080d9  _errno                          
abs   00000300  __STACK_SIZE                    
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
abs   ffffffff  ___pinit__                      
abs   ffffffff  binit                           
abs   ffffffff  pinit                           

[83 symbols]
