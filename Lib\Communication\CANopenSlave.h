#ifndef _CA<PERSON><PERSON><PERSON>_SALVE_H_
#define _<PERSON><PERSON><PERSON><PERSON>_SALVE_H_

#define CANOPEN_SLAVE_TXPDO_COUNT_MAX                                   10

typedef enum
{
    ENUM_CANOPEN_SLAVE_INIT              = 0,
    <PERSON><PERSON><PERSON>_CANOPEN_SLAVE_STOP              = 4,
    <PERSON><PERSON><PERSON>_CANOPEN_SLAVE_OPERATIONAL       = 5,
    ENUM_CANOPEN_SLAVE_PRE_OPERATIONAL   = 127
}ENUM_CANOPEN_SLAVE_STATUS;

typedef enum
{
    //Send
    ENUM_CANOPEN_SLAVE_MAIL_BOX_SEND_SDO_RESPONSE                                    = 0,
    <PERSON>NU<PERSON>_CA<PERSON>OP<PERSON>_SLAVE_MAIL_BOX_SEND_TXPDO0                                          = 1,
    <PERSON><PERSON><PERSON>_CANOPEN_SLAVE_MAIL_BOX_SEND_TXPDO1                                          = 2,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_SEND_TXPDO2                                          = 3,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_SEND_TXPDO3                                          = 4,
    <PERSON>NU<PERSON>_CANOPEN_SLAVE_MAIL_BOX_SEND_TXPDO4                                          = 5,
    <PERSON><PERSON><PERSON>_CANOPEN_SLAVE_MAIL_BOX_SEND_HEARTBEAT_NODEGUARDING_BOOTUP                   = 6,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_SEND_EMERGENCY                                       = 7,

    //Receive
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_NMT                                          = 16,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_SYNC                                         = 17,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_RXPDO0                                       = 18,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_RXPDO1                                       = 19,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_RXPDO2                                       = 20,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_RXPDO3                                       = 21,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_SDO_REQUEST                                  = 22,
    ENUM_CANOPEN_SLAVE_MAIL_BOX_RECEIVE_HEARTBEAT_NODEGUARDING_BOOTUP                = 23

}ENUM_CANOPEN_SLAVE_MAIL_BOX_INDEX;

typedef struct
{
    unsigned int Ident;
    unsigned int NoOfBytes;
    unsigned int NewMsg     :1;
    unsigned int Inhibit    :1;
    unsigned char BYTE[8]  ;
}STRUCT_CANOPEN_SLAVE_MESSAGE_PROPERTY;

typedef struct
{
    unsigned int  index;
    unsigned char subindex;
    unsigned char attribute;
    unsigned char length;
    void* pData;
} STRUCT_CANOPEN_SLAVE_BOJECT_DICTIONARY_ENTRY;


extern volatile STRUCT_CANOPEN_SLAVE_MESSAGE_PROPERTY CANopenSlaveRXMessage[32];
extern volatile STRUCT_CANOPEN_SLAVE_MESSAGE_PROPERTY CANopenSlaveTXMessage[32];

/*************************************************************************************************************************************************************/




extern ENUM_CANOPEN_SLAVE_STATUS  CANopenSlaveStatus;

extern volatile unsigned char CANopenSlaveNMTMessageFlag;
extern volatile unsigned char CANopenSlaveNMTMessageCommand;

extern volatile unsigned char CANopenSlaveTXPDOSyncTimer[CANOPEN_SLAVE_TXPDO_COUNT_MAX];
extern volatile unsigned int  CANopenSlaveTXPDOInhibitTimer[CANOPEN_SLAVE_TXPDO_COUNT_MAX];
extern volatile unsigned int  CANopenSlaveTXPDOEventTimer[CANOPEN_SLAVE_TXPDO_COUNT_MAX];

extern unsigned char  CANopenSlaveTXPDOEnable[CANOPEN_SLAVE_TXPDO_COUNT_MAX];

//CANOPENͨѶ
extern void CANOpenSlaveStatusInit(void);
extern void CANOpenSlaveNMTControl(unsigned char NodeID,unsigned int CycleTime);
extern void CANOpenSlaveReceiveData(unsigned int CycleTime);
extern void CANOpenSlaveSendData(unsigned int CycleTime);
extern void CANOpenSlaveBaudrateSet(int BaudrateOption);
extern STRUCT_CANOPEN_SLAVE_BOJECT_DICTIONARY_ENTRY *CANOpenSlaveFindEntryInOD(unsigned int Index, unsigned char Subindex);

extern void CANOpenSlave_TXMessageToBuffers(int INDEX);

#endif
