################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
CMD_SRCS += \
../28335_RAM_lnk.cmd 

ASM_SRCS += \
../CodeStartBranch.asm 

C_SRCS += \
../ServoSimCore.c \
../externInputInterface.c \
../main.c \
../stateMachine.c 

C_DEPS += \
./ServoSimCore.d \
./externInputInterface.d \
./main.d \
./stateMachine.d 

OBJS += \
./CodeStartBranch.obj \
./ServoSimCore.obj \
./externInputInterface.obj \
./main.obj \
./stateMachine.obj 

ASM_DEPS += \
./CodeStartBranch.d 

OBJS__QUOTED += \
"CodeStartBranch.obj" \
"ServoSimCore.obj" \
"externInputInterface.obj" \
"main.obj" \
"stateMachine.obj" 

C_DEPS__QUOTED += \
"ServoSimCore.d" \
"externInputInterface.d" \
"main.d" \
"stateMachine.d" 

ASM_DEPS__QUOTED += \
"CodeStartBranch.d" 

ASM_SRCS__QUOTED += \
"../CodeStartBranch.asm" 

C_SRCS__QUOTED += \
"../ServoSimCore.c" \
"../externInputInterface.c" \
"../main.c" \
"../stateMachine.c" 


