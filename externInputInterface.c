/**
 * @file externInputInterface.c
 * @brief External input interface implementation
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#include "externInputInterface.h"
#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "Z_Application\StateMachine\ServoSimCore.h"

/* External servo simulation state - declared in ServoSimCore */
extern ServoSimState gSTservoSimState;

/* External global state variables - declared in stateMachine.c */
extern stGlobalVars gstSEC;

void Input(void) {
    /* Input from real device */

    gstSEC.gbSafetyLineFb = TRUE;
    gstSEC.gbTCUNormalModeCmd = TRUE;

}

void Output(void) {

}

void MotorControlInterface_Run(ENUM_MOTION_CONTROL_MODE_SELECT Mode,
                              unsigned int CycleTime,
                              float TargetPosition,
                              float TargetSpeed,
                              float MaxSpeed,
                              float KP1,
                              float KP2) {

    /* Development/Test version: Use virtual simulation */
    gSTservoSimState.iRunning = 1;
    if (Mode == MOTION_CONTROL_MODE_POSITION){
        gSTservoSimState.iModeControl = 0;
        gSTservoSimState.dPositionCommand = ((float)TargetPosition * 0.01f) * (float)gstSEC.guiGearRatioAll;
        gSTservoSimState.dSpeedLimit = (float)MaxSpeed * 0.01f * (float)gstSEC.guiGearRatioAll;
    }
    else{
        gSTservoSimState.iModeControl = 1;
        gSTservoSimState.dSpeedLimit = (float)MaxSpeed * 0.01f * (float)gstSEC.guiGearRatioAll;
        gSTservoSimState.dMotionSpeedCommand = (float)TargetSpeed * 0.01f * (float)gstSEC.guiGearRatioAll;
    }

    /* Update simulation state */
    SimulationStep(&gSTservoSimState);

    /* Convert simulation results back to system format */
    if (gstSEC.guiGearRatioAll != 0U) {
        gstSEC.giMotorPosition = (i16)((float)(gSTservoSimState.dMotorPosition * 100.0 / (double)gstSEC.guiGearRatioAll));
        gstSEC.giMotorSpeed = (i16)((float)(gSTservoSimState.dMotorSpeed * 100.0 / (double)gstSEC.guiGearRatioAll));
    }

}

