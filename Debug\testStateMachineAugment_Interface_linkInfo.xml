<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v18.1.4.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x688f0f64</link_time>
   <link_errors>0x0</link_errors>
   <output_file>testStateMachineAugment_Interface.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x59a2</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\</path>
         <kind>object</kind>
         <file>CodeStartBranch.obj</file>
         <name>CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\</path>
         <kind>object</kind>
         <file>ServoSimCore.obj</file>
         <name>ServoSimCore.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\</path>
         <kind>object</kind>
         <file>externInputInterface.obj</file>
         <name>externInputInterface.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\</path>
         <kind>object</kind>
         <file>stateMachine.obj</file>
         <name>stateMachine.obj</name>
      </input_file>
      <input_file id="fl-a">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>e_fmodf.c.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>e_sqrtf.c.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fs_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-1d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-1e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>errno.c.obj</name>
      </input_file>
      <input_file id="fl-1f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-20">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-2a">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.cinit</name>
         <load_address>0x4038</load_address>
         <run_address>0x4038</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-22">
         <name>.cinit</name>
         <load_address>0x4046</load_address>
         <run_address>0x4046</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-77">
         <name>.cinit:__lock</name>
         <load_address>0x4050</load_address>
         <run_address>0x4050</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.cinit:__unlock</name>
         <load_address>0x4055</load_address>
         <run_address>0x4055</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.cinit</name>
         <load_address>0x405a</load_address>
         <run_address>0x405a</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x58a</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.text</name>
         <load_address>0x558a</load_address>
         <run_address>0x558a</run_address>
         <size>0x229</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.text</name>
         <load_address>0x57b3</load_address>
         <run_address>0x57b3</run_address>
         <size>0xce</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text</name>
         <load_address>0x5881</load_address>
         <run_address>0x5881</run_address>
         <size>0x99</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-41">
         <name>.text</name>
         <load_address>0x591a</load_address>
         <run_address>0x591a</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-11">
         <name>.text</name>
         <load_address>0x59a2</load_address>
         <run_address>0x59a2</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text</name>
         <load_address>0x59f8</load_address>
         <run_address>0x59f8</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text</name>
         <load_address>0x5a21</load_address>
         <run_address>0x5a21</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text</name>
         <load_address>0x5a49</load_address>
         <run_address>0x5a49</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.text</name>
         <load_address>0x5a4a</load_address>
         <run_address>0x5a4a</run_address>
         <size>0x26</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text</name>
         <load_address>0x5a70</load_address>
         <run_address>0x5a70</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text</name>
         <load_address>0x5a94</load_address>
         <run_address>0x5a94</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text</name>
         <load_address>0x5ab1</load_address>
         <run_address>0x5ab1</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-24">
         <name>.text:retain</name>
         <load_address>0x5aca</load_address>
         <run_address>0x5aca</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text</name>
         <load_address>0x5ad4</load_address>
         <run_address>0x5ad4</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text</name>
         <load_address>0x5add</load_address>
         <run_address>0x5add</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-12">
         <name>codestart</name>
         <load_address>0x409f</load_address>
         <run_address>0x409f</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-75">
         <name>ramfuncs</name>
         <load_address>0x4060</load_address>
         <run_address>0x4060</run_address>
         <size>0x3f</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-23">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8092</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8000</run_address>
         <size>0x8c</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x809a</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-60">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x808c</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-80">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8098</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-76">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8096</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-81">
         <name>.econst:_Zero</name>
         <load_address>0x809c</load_address>
         <run_address>0x809c</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-31">
         <name>.econst</name>
         <load_address>0x80a0</load_address>
         <run_address>0x80a0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe4</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17">
         <name>.debug_info</name>
         <load_address>0xe4</load_address>
         <run_address>0xe4</run_address>
         <size>0x17df</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.debug_info</name>
         <load_address>0x18c3</load_address>
         <run_address>0x18c3</run_address>
         <size>0x1537</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x2dfa</load_address>
         <run_address>0x2dfa</run_address>
         <size>0xcb2</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-29">
         <name>.debug_info</name>
         <load_address>0x3aac</load_address>
         <run_address>0x3aac</run_address>
         <size>0x2811</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_info</name>
         <load_address>0x62bd</load_address>
         <run_address>0x62bd</run_address>
         <size>0x4f8</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x67b5</load_address>
         <run_address>0x67b5</run_address>
         <size>0x426</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_info</name>
         <load_address>0x6bdb</load_address>
         <run_address>0x6bdb</run_address>
         <size>0x176</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_info</name>
         <load_address>0x6d51</load_address>
         <run_address>0x6d51</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x6e68</load_address>
         <run_address>0x6e68</run_address>
         <size>0x58a</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_info</name>
         <load_address>0x73f2</load_address>
         <run_address>0x73f2</run_address>
         <size>0x485</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_info</name>
         <load_address>0x7877</load_address>
         <run_address>0x7877</run_address>
         <size>0x3f8</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_info</name>
         <load_address>0x7c6f</load_address>
         <run_address>0x7c6f</run_address>
         <size>0x3f5</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_info</name>
         <load_address>0x8064</load_address>
         <run_address>0x8064</run_address>
         <size>0x1c6</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x822a</load_address>
         <run_address>0x822a</run_address>
         <size>0x58f</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_info</name>
         <load_address>0x87b9</load_address>
         <run_address>0x87b9</run_address>
         <size>0x517</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_info</name>
         <load_address>0x8cd0</load_address>
         <run_address>0x8cd0</run_address>
         <size>0x4b2</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x9182</load_address>
         <run_address>0x9182</run_address>
         <size>0xaa</size>
      </object_component>
      <object_component id="oc-14">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_line</name>
         <load_address>0x45</load_address>
         <run_address>0x45</run_address>
         <size>0x2ae</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x2f3</load_address>
         <run_address>0x2f3</run_address>
         <size>0x96</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-26">
         <name>.debug_line</name>
         <load_address>0x389</load_address>
         <run_address>0x389</run_address>
         <size>0x10b</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.debug_line</name>
         <load_address>0x494</load_address>
         <run_address>0x494</run_address>
         <size>0x3ea</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-33">
         <name>.debug_line</name>
         <load_address>0x87e</load_address>
         <run_address>0x87e</run_address>
         <size>0xb7</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x935</load_address>
         <run_address>0x935</run_address>
         <size>0x57</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x98c</load_address>
         <run_address>0x98c</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_line</name>
         <load_address>0xa0a</load_address>
         <run_address>0xa0a</run_address>
         <size>0xad</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0xab7</load_address>
         <run_address>0xab7</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0xb07</load_address>
         <run_address>0xb07</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_line</name>
         <load_address>0xb69</load_address>
         <run_address>0xb69</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_line</name>
         <load_address>0xba7</load_address>
         <run_address>0xba7</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_line</name>
         <load_address>0xbe1</load_address>
         <run_address>0xbe1</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_line</name>
         <load_address>0xc46</load_address>
         <run_address>0xc46</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0xca4</load_address>
         <run_address>0xca4</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-15">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.debug_abbrev</name>
         <load_address>0x21</load_address>
         <run_address>0x21</run_address>
         <size>0x16d</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_abbrev</name>
         <load_address>0x18e</load_address>
         <run_address>0x18e</run_address>
         <size>0xf3</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-27">
         <name>.debug_abbrev</name>
         <load_address>0x281</load_address>
         <run_address>0x281</run_address>
         <size>0x150</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2e">
         <name>.debug_abbrev</name>
         <load_address>0x3d1</load_address>
         <run_address>0x3d1</run_address>
         <size>0x1ed</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x5be</load_address>
         <run_address>0x5be</run_address>
         <size>0xc1</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x67f</load_address>
         <run_address>0x67f</run_address>
         <size>0xa4</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_abbrev</name>
         <load_address>0x723</load_address>
         <run_address>0x723</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_abbrev</name>
         <load_address>0x769</load_address>
         <run_address>0x769</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x7a1</load_address>
         <run_address>0x7a1</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_abbrev</name>
         <load_address>0x884</load_address>
         <run_address>0x884</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_abbrev</name>
         <load_address>0x92c</load_address>
         <run_address>0x92c</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x994</load_address>
         <run_address>0x994</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x9fa</load_address>
         <run_address>0x9fa</run_address>
         <size>0x31</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xa2b</load_address>
         <run_address>0xa2b</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_abbrev</name>
         <load_address>0xb5b</load_address>
         <run_address>0xb5b</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_abbrev</name>
         <load_address>0xc0d</load_address>
         <run_address>0xc0d</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_abbrev</name>
         <load_address>0xcfb</load_address>
         <run_address>0xcfb</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-16">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x90</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.debug_aranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-28">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_aranges</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0x100</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_aranges</name>
         <load_address>0x228</load_address>
         <run_address>0x228</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_aranges</name>
         <load_address>0x248</load_address>
         <run_address>0x248</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_aranges</name>
         <load_address>0x268</load_address>
         <run_address>0x268</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x288</load_address>
         <run_address>0x288</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_aranges</name>
         <load_address>0x2a8</load_address>
         <run_address>0x2a8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_aranges</name>
         <load_address>0x2c8</load_address>
         <run_address>0x2c8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_aranges</name>
         <load_address>0x2e8</load_address>
         <run_address>0x2e8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_aranges</name>
         <load_address>0x308</load_address>
         <run_address>0x308</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_aranges</name>
         <load_address>0x328</load_address>
         <run_address>0x328</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_aranges</name>
         <load_address>0x350</load_address>
         <run_address>0x350</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_aranges</name>
         <load_address>0x380</load_address>
         <run_address>0x380</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-18">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e4</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x1e4</load_address>
         <run_address>0x1e4</run_address>
         <size>0x7c</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-25">
         <name>.debug_frame</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0xd4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2c">
         <name>.debug_frame</name>
         <load_address>0x334</load_address>
         <run_address>0x334</run_address>
         <size>0x300</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x634</load_address>
         <run_address>0x634</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x684</load_address>
         <run_address>0x684</run_address>
         <size>0x4c</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_frame</name>
         <load_address>0x6d0</load_address>
         <run_address>0x6d0</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_frame</name>
         <load_address>0x738</load_address>
         <run_address>0x738</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_frame</name>
         <load_address>0x780</load_address>
         <run_address>0x780</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_frame</name>
         <load_address>0x7c8</load_address>
         <run_address>0x7c8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0x810</load_address>
         <run_address>0x810</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_frame</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x78</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x8f0</load_address>
         <run_address>0x8f0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x60</size>
         <contents>
            <object_component_ref idref="oc-2a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-5b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0xadf</size>
         <contents>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-11"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-6f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x409f</load_address>
         <run_address>0x409f</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-12"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x4060</load_address>
         <run_address>0x4060</run_address>
         <size>0x3f</size>
         <contents>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-3c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>vectors</name>
         <run_address>0x3fffc2</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x0</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-9c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0x8000</run_address>
         <size>0x9b</size>
         <contents>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-76"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0x809c</load_address>
         <run_address>0x809c</run_address>
         <size>0x6</size>
         <contents>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-31"/>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-90" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x922c</size>
         <contents>
            <object_component_ref idref="oc-13"/>
            <object_component_ref idref="oc-17"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-29"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-9d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-92" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf9</size>
         <contents>
            <object_component_ref idref="oc-14"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-26"/>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-94" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd0a</size>
         <contents>
            <object_component_ref idref="oc-15"/>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-27"/>
            <object_component_ref idref="oc-2e"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-9e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-96" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a0</size>
         <contents>
            <object_component_ref idref="oc-16"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-28"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-98" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x938</size>
         <contents>
            <object_component_ref idref="oc-18"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6b"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>ZONE0</name>
         <page_id>0x0</page_id>
         <origin>0x4000</origin>
         <length>0x1000</length>
         <used_space>0xa1</used_space>
         <unused_space>0xf5f</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4000</start_address>
               <size>0x60</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4060</start_address>
               <size>0x3f</size>
               <logical_group_ref idref="lg-6"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x409f</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x40a1</start_address>
               <size>0xf5f</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE1</name>
         <page_id>0x0</page_id>
         <origin>0x5000</origin>
         <length>0x1000</length>
         <used_space>0xadf</used_space>
         <unused_space>0x521</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0xadf</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x5adf</start_address>
               <size>0x521</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE2</name>
         <page_id>0x0</page_id>
         <origin>0x6000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE3</name>
         <page_id>0x0</page_id>
         <origin>0x7000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE4</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE5</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x0</used_space>
         <unused_space>0x9</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>VECTORS</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc2</origin>
         <length>0x3e</length>
         <used_space>0x0</used_space>
         <unused_space>0x3e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x0</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0x300</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x0</used_space>
         <unused_space>0x400</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIEDATA</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x0</used_space>
         <unused_space>0x20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0xa1</used_space>
         <unused_space>0xf5f</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x9b</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0x809b</start_address>
               <size>0x1</size>
            </available_space>
            <allocated_space>
               <start_address>0x809c</start_address>
               <size>0x6</size>
               <logical_group_ref idref="lg-c"/>
            </allocated_space>
            <available_space>
               <start_address>0x80a2</start_address>
               <size>0xf5e</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L2SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L3SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0x5adf</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0x5adf</value>
      </symbol>
      <symbol id="sm-d">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-41">
         <name>code_start</name>
         <value>0x409f</value>
         <object_component_ref idref="oc-12"/>
      </symbol>
      <symbol id="sm-6c">
         <name>_DegToRad</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-6d">
         <name>_SimulationStep</name>
         <value>0x54dd</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-6e">
         <name>_ResetSimulation</name>
         <value>0x5535</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-6f">
         <name>_InitSimulation</name>
         <value>0x512f</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-70">
         <name>_RadToDeg</name>
         <value>0x5011</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-7f">
         <name>_MotorControlInterface_Run</name>
         <value>0x5889</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-80">
         <name>_Output</name>
         <value>0x5888</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-81">
         <name>_Input</name>
         <value>0x5881</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-a0">
         <name>_main</name>
         <value>0x4075</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-a1">
         <name>_InitSystem</name>
         <value>0x4060</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-a2">
         <name>_DefaultIsr</name>
         <value>0x5aca</value>
         <object_component_ref idref="oc-24"/>
      </symbol>
      <symbol id="sm-a3">
         <name>__c_int00</name>
         <value>0x5a43</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-a4">
         <name>_DelayMs</name>
         <value>0x5a21</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-a5">
         <name>_InitPieVectTable</name>
         <value>0x5a3f</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-10f">
         <name>_StatemachineRunToEmergencyFlag</name>
         <value>0x800e</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-110">
         <name>_guiTf910Condition</name>
         <value>0x8001</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-111">
         <name>_SetBit</name>
         <value>0x558a</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-112">
         <name>_guiTf36Condition</name>
         <value>0x8004</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-113">
         <name>_MotorRun</name>
         <value>0x55a9</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-114">
         <name>_ClrBit</name>
         <value>0x5599</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-115">
         <name>_guiTf18Condition</name>
         <value>0x800a</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-116">
         <name>_guiTf64Condition</name>
         <value>0x8002</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-117">
         <name>_gSTservoSimState</name>
         <value>0x8040</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-118">
         <name>_StateMachineInit</name>
         <value>0x55c4</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-119">
         <name>_guiTf13Condition</name>
         <value>0x8003</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11a">
         <name>_guiTf73Condition</name>
         <value>0x800d</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11b">
         <name>_guiTf65Condition</name>
         <value>0x8000</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11c">
         <name>_guiTf53Condition</name>
         <value>0x8005</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11d">
         <name>_guiTf57Condition</name>
         <value>0x800b</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11e">
         <name>_guiTf45Condition</name>
         <value>0x800c</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-11f">
         <name>_guiTf95Condition</name>
         <value>0x8008</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-120">
         <name>_guiTf83Condition</name>
         <value>0x8006</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-121">
         <name>_stcStateMachine</name>
         <value>0x800f</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-122">
         <name>_gCurrentState</name>
         <value>0x8007</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-123">
         <name>_StateMachineProcess</name>
         <value>0x566b</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-124">
         <name>_gstSEC</name>
         <value>0x8013</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-125">
         <name>_guiTf105Condition</name>
         <value>0x8009</value>
         <object_component_ref idref="oc-2b"/>
      </symbol>
      <symbol id="sm-151">
         <name>_fmodf</name>
         <value>0x57b3</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-152">
         <name>_fmod</name>
         <value>0x57b3</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-162">
         <name>_sqrtf</name>
         <value>0x5a4a</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-163">
         <name>_sqrt</name>
         <value>0x5a4a</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-17e">
         <name>_c_int00</name>
         <value>0x59a2</value>
         <object_component_ref idref="oc-11"/>
      </symbol>
      <symbol id="sm-17f">
         <name>__stack</name>
         <value>0x0</value>
         <object_component_ref idref="oc-3b"/>
      </symbol>
      <symbol id="sm-190">
         <name>FS$$DIV</name>
         <value>0x591a</value>
         <object_component_ref idref="oc-41"/>
      </symbol>
      <symbol id="sm-19f">
         <name>_copy_in</name>
         <value>0x5a70</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-1b0">
         <name>_memcpy</name>
         <value>0x5a94</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>__system_pre_init</name>
         <value>0x5add</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-1c9">
         <name>__system_post_cinit</name>
         <value>0x5a49</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-1d3">
         <name>_errno</name>
         <value>0x809a</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-1e7">
         <name>C$$EXIT</name>
         <value>0x59f8</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1e8">
         <name>_exit</name>
         <value>0x59fa</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1e9">
         <name>___TI_cleanup_ptr</name>
         <value>0x808e</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-1ea">
         <name>___TI_enable_exit_profile_output</name>
         <value>0x808c</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-1eb">
         <name>_abort</name>
         <value>0x59f8</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1ec">
         <name>___TI_dtors_ptr</name>
         <value>0x8090</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-200">
         <name>__unlock</name>
         <value>0x8098</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-201">
         <name>__lock</name>
         <value>0x8096</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-202">
         <name>__register_lock</name>
         <value>0x5ad8</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-203">
         <name>__nop</name>
         <value>0x5adc</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-204">
         <name>__register_unlock</name>
         <value>0x5ad4</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-213">
         <name>__args_main</name>
         <value>0x5ab1</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
